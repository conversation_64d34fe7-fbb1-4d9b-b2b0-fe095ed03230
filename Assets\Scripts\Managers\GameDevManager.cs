using UnityEngine;
using System.Collections.Generic;

namespace ConsoleTycoon
{
    public class GameDevManager : MonoBehaviour
    {
        [<PERSON><PERSON>("Game Development")]
        public List<GameGenreData> genreData = new List<GameGenreData>();
        public List<GameTemplate> gameTemplates = new List<GameTemplate>();
        
        [Header("Market Trends")]
        public Dictionary<GameGenre, float> genrePopularity = new Dictionary<GameGenre, float>();
        public List<TrendingFeature> trendingFeatures = new List<TrendingFeature>();
        
        private CompanyData playerCompany;
        
        private void Start()
        {
            playerCompany = GameManager.Instance?.playerCompany;
        }
        
        public void Initialize()
        {
            InitializeGenreData();
            InitializeGameTemplates();
            InitializeGenrePopularity();
            UpdateTrendingFeatures();
            
            Debug.Log("Game Development Manager initialized");
        }
        
        private void InitializeGenreData()
        {
            genreData.Clear();
            
            genreData.Add(new GameGenreData
            {
                genre = GameGenre.Action,
                baseDevTime = 6,
                baseCost = 100000,
                difficultyMultiplier = 1.0f,
                popularityTrend = 1.2f,
                requiredSkills = new Dictionary<EmployeeRole, int> 
                { 
                    { EmployeeRole.Programmer, 70 }, 
                    { EmployeeRole.Designer, 60 } 
                }
            });
            
            genreData.Add(new GameGenreData
            {
                genre = GameGenre.RPG,
                baseDevTime = 12,
                baseCost = 200000,
                difficultyMultiplier = 1.5f,
                popularityTrend = 1.1f,
                requiredSkills = new Dictionary<EmployeeRole, int> 
                { 
                    { EmployeeRole.Programmer, 60 }, 
                    { EmployeeRole.Designer, 80 } 
                }
            });
            
            genreData.Add(new GameGenreData
            {
                genre = GameGenre.Strategy,
                baseDevTime = 10,
                baseCost = 175000,
                difficultyMultiplier = 1.3f,
                popularityTrend = 0.9f,
                requiredSkills = new Dictionary<EmployeeRole, int> 
                { 
                    { EmployeeRole.Programmer, 75 }, 
                    { EmployeeRole.Designer, 70 } 
                }
            });
            
            genreData.Add(new GameGenreData
            {
                genre = GameGenre.Sports,
                baseDevTime = 8,
                baseCost = 150000,
                difficultyMultiplier = 1.1f,
                popularityTrend = 1.0f,
                requiredSkills = new Dictionary<EmployeeRole, int> 
                { 
                    { EmployeeRole.Programmer, 65 }, 
                    { EmployeeRole.Designer, 65 } 
                }
            });
            
            genreData.Add(new GameGenreData
            {
                genre = GameGenre.Racing,
                baseDevTime = 8,
                baseCost = 150000,
                difficultyMultiplier = 1.1f,
                popularityTrend = 0.95f,
                requiredSkills = new Dictionary<EmployeeRole, int> 
                { 
                    { EmployeeRole.Programmer, 70 }, 
                    { EmployeeRole.Designer, 60 } 
                }
            });
            
            genreData.Add(new GameGenreData
            {
                genre = GameGenre.Puzzle,
                baseDevTime = 3,
                baseCost = 50000,
                difficultyMultiplier = 0.8f,
                popularityTrend = 0.8f,
                requiredSkills = new Dictionary<EmployeeRole, int> 
                { 
                    { EmployeeRole.Programmer, 50 }, 
                    { EmployeeRole.Designer, 70 } 
                }
            });
            
            genreData.Add(new GameGenreData
            {
                genre = GameGenre.Platformer,
                baseDevTime = 4,
                baseCost = 75000,
                difficultyMultiplier = 0.9f,
                popularityTrend = 1.0f,
                requiredSkills = new Dictionary<EmployeeRole, int> 
                { 
                    { EmployeeRole.Programmer, 60 }, 
                    { EmployeeRole.Designer, 75 } 
                }
            });
        }
        
        private void InitializeGameTemplates()
        {
            gameTemplates.Clear();
            
            gameTemplates.Add(new GameTemplate
            {
                name = "Quick Arcade Game",
                description = "A simple, fast-to-develop arcade-style game",
                baseDevTime = 2,
                baseCost = 25000,
                targetPrice = 30,
                suitableGenres = new List<GameGenre> { GameGenre.Action, GameGenre.Puzzle, GameGenre.Platformer }
            });
            
            gameTemplates.Add(new GameTemplate
            {
                name = "Standard Game",
                description = "A full-featured game with good production values",
                baseDevTime = 6,
                baseCost = 100000,
                targetPrice = 60,
                suitableGenres = new List<GameGenre> { GameGenre.Action, GameGenre.Sports, GameGenre.Racing }
            });
            
            gameTemplates.Add(new GameTemplate
            {
                name = "AAA Blockbuster",
                description = "A high-budget, premium game with cutting-edge features",
                baseDevTime = 18,
                baseCost = 500000,
                targetPrice = 70,
                suitableGenres = new List<GameGenre> { GameGenre.RPG, GameGenre.Action, GameGenre.Strategy }
            });
        }
        
        private void InitializeGenrePopularity()
        {
            genrePopularity.Clear();
            
            // Initialize with base popularity values
            genrePopularity[GameGenre.Action] = 1.2f;
            genrePopularity[GameGenre.RPG] = 1.1f;
            genrePopularity[GameGenre.Strategy] = 0.9f;
            genrePopularity[GameGenre.Sports] = 1.0f;
            genrePopularity[GameGenre.Racing] = 0.95f;
            genrePopularity[GameGenre.Puzzle] = 0.8f;
            genrePopularity[GameGenre.Platformer] = 1.0f;
            genrePopularity[GameGenre.Adventure] = 0.9f;
            genrePopularity[GameGenre.Simulation] = 0.85f;
            genrePopularity[GameGenre.Fighting] = 0.9f;
        }
        
        public GameProject StartGameProject(string gameTitle, GameGenre genre, string targetConsole, GameTemplate template = null)
        {
            if (playerCompany == null) return null;
            
            GameProject project = new GameProject(gameTitle, genre, targetConsole);
            
            var genreInfo = GetGenreData(genre);
            if (genreInfo != null)
            {
                project.totalDevelopmentTime = genreInfo.baseDevTime;
                project.totalBudget = genreInfo.baseCost;
                project.monthlyBurnRate = genreInfo.baseCost / genreInfo.baseDevTime;
                
                // Set required team size based on genre
                project.requiredProgrammers = GetRequiredProgrammers(genre);
                project.requiredDesigners = GetRequiredDesigners(genre);
            }
            
            if (template != null)
            {
                ApplyTemplate(project, template);
            }
            
            playerCompany.activeGameProjects.Add(project);
            
            Debug.Log($"Started game project: {project.projectName}");
            return project;
        }
        
        private void ApplyTemplate(GameProject project, GameTemplate template)
        {
            // Modify project based on template
            float timeMultiplier = (float)template.baseDevTime / 6f; // 6 is standard
            float costMultiplier = (float)template.baseCost / 100000f; // 100k is standard
            
            project.totalDevelopmentTime = Mathf.RoundToInt(project.totalDevelopmentTime * timeMultiplier);
            project.totalBudget = (long)(project.totalBudget * costMultiplier);
            project.targetPrice = template.targetPrice;
            project.monthlyBurnRate = project.totalBudget / project.totalDevelopmentTime;
        }
        
        public GameGenreData GetGenreData(GameGenre genre)
        {
            return genreData.Find(g => g.genre == genre);
        }
        
        public float GetGenrePopularity(GameGenre genre)
        {
            return genrePopularity.ContainsKey(genre) ? genrePopularity[genre] : 1.0f;
        }
        
        public int GetRequiredProgrammers(GameGenre genre)
        {
            switch (genre)
            {
                case GameGenre.Puzzle: return 1;
                case GameGenre.Platformer: return 1;
                case GameGenre.Action: return 2;
                case GameGenre.Sports: return 2;
                case GameGenre.Racing: return 2;
                case GameGenre.Strategy: return 3;
                case GameGenre.RPG: return 3;
                default: return 2;
            }
        }
        
        public int GetRequiredDesigners(GameGenre genre)
        {
            switch (genre)
            {
                case GameGenre.Puzzle: return 1;
                case GameGenre.Action: return 1;
                case GameGenre.Sports: return 1;
                case GameGenre.Racing: return 1;
                case GameGenre.Platformer: return 2;
                case GameGenre.Strategy: return 2;
                case GameGenre.RPG: return 3;
                default: return 1;
            }
        }
        
        public bool CanDevelopForConsole(string consoleName)
        {
            if (playerCompany == null) return false;
            
            // Check if player owns the console or has development rights
            var console = playerCompany.releasedConsoles.Find(c => c.name == consoleName);
            if (console != null) return true;
            
            // Check if it's a third-party console (would need licensing)
            // For now, assume all consoles are available for development
            return true;
        }
        
        public long CalculateDevelopmentCost(GameGenre genre, int teamSize, int developmentTime)
        {
            var genreInfo = GetGenreData(genre);
            if (genreInfo == null) return 100000;
            
            long baseCost = genreInfo.baseCost;
            
            // Team size multiplier
            float teamMultiplier = teamSize / 3f; // 3 is standard team size
            
            // Time multiplier
            float timeMultiplier = developmentTime / (float)genreInfo.baseDevTime;
            
            // Difficulty multiplier
            float difficultyMultiplier = genreInfo.difficultyMultiplier;
            
            return (long)(baseCost * teamMultiplier * timeMultiplier * difficultyMultiplier);
        }
        
        public void UpdateTrendingFeatures()
        {
            trendingFeatures.Clear();
            
            int currentYear = GameManager.Instance?.timeManager?.currentYear ?? 1980;
            
            // Add trending features based on current year
            if (currentYear >= 1985)
            {
                trendingFeatures.Add(new TrendingFeature("Save System", 1.1f, "Players want to save their progress"));
            }
            
            if (currentYear >= 1990)
            {
                trendingFeatures.Add(new TrendingFeature("Multiple Difficulty Levels", 1.05f, "Different skill levels for different players"));
            }
            
            if (currentYear >= 1995)
            {
                trendingFeatures.Add(new TrendingFeature("Multiplayer", 1.2f, "Playing with friends is popular"));
                trendingFeatures.Add(new TrendingFeature("3D Graphics", 1.3f, "3D is the future of gaming"));
            }
            
            if (currentYear >= 2000)
            {
                trendingFeatures.Add(new TrendingFeature("Online Features", 1.25f, "Internet connectivity is becoming important"));
                trendingFeatures.Add(new TrendingFeature("Customization", 1.1f, "Players want to personalize their experience"));
            }
            
            if (currentYear >= 2005)
            {
                trendingFeatures.Add(new TrendingFeature("Achievement System", 1.15f, "Players love collecting achievements"));
                trendingFeatures.Add(new TrendingFeature("DLC Support", 1.1f, "Downloadable content extends game life"));
            }
        }
        
        public void UpdateGenrePopularity()
        {
            // Simulate market trends affecting genre popularity
            foreach (var genre in System.Enum.GetValues(typeof(GameGenre)))
            {
                GameGenre g = (GameGenre)genre;
                if (genrePopularity.ContainsKey(g))
                {
                    // Add some random fluctuation
                    float change = Random.Range(-0.05f, 0.05f);
                    genrePopularity[g] = Mathf.Clamp(genrePopularity[g] + change, 0.5f, 2.0f);
                }
            }
        }
        
        public List<GameGenre> GetRecommendedGenres(int count = 3)
        {
            var genres = new List<GameGenre>();
            var sortedGenres = new List<System.Collections.Generic.KeyValuePair<GameGenre, float>>();
            
            foreach (var kvp in genrePopularity)
            {
                sortedGenres.Add(kvp);
            }
            
            sortedGenres.Sort((a, b) => b.Value.CompareTo(a.Value));
            
            for (int i = 0; i < Mathf.Min(count, sortedGenres.Count); i++)
            {
                genres.Add(sortedGenres[i].Key);
            }
            
            return genres;
        }
        
        public void LaunchGame(Game game)
        {
            if (game == null) return;
            
            game.Launch();
            
            // Add to market
            if (GameManager.Instance?.marketManager != null)
            {
                GameManager.Instance.marketManager.AddPlayerGameToMarket(game);
            }
            
            Debug.Log($"Launched game: {game.title}");
        }
        
        public List<string> GetAvailableConsoles()
        {
            var consoles = new List<string>();
            
            if (playerCompany != null)
            {
                // Add player's own consoles
                foreach (var console in playerCompany.releasedConsoles)
                {
                    if (console.isActive)
                    {
                        consoles.Add(console.name);
                    }
                }
            }
            
            // Add third-party consoles (simplified - in reality would need licensing)
            if (GameManager.Instance?.marketManager != null)
            {
                var marketConsoles = GameManager.Instance.marketManager.GetAvailableConsoles();
                consoles.AddRange(marketConsoles);
            }
            
            return consoles;
        }
    }
    
    [System.Serializable]
    public class GameGenreData
    {
        public GameGenre genre;
        public int baseDevTime; // Months
        public long baseCost;
        public float difficultyMultiplier;
        public float popularityTrend;
        public Dictionary<EmployeeRole, int> requiredSkills = new Dictionary<EmployeeRole, int>();
    }
    
    [System.Serializable]
    public class GameTemplate
    {
        public string name;
        public string description;
        public int baseDevTime;
        public long baseCost;
        public long targetPrice;
        public List<GameGenre> suitableGenres = new List<GameGenre>();
    }
    
    [System.Serializable]
    public class TrendingFeature
    {
        public string name;
        public float popularityMultiplier;
        public string description;
        
        public TrendingFeature(string featureName, float multiplier, string desc)
        {
            name = featureName;
            popularityMultiplier = multiplier;
            description = desc;
        }
    }
}
