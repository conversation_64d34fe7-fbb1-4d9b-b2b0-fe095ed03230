using UnityEngine;

namespace ConsoleTycoon
{
    [System.Serializable]
    public class Employee
    {
        [<PERSON><PERSON>("Basic Information")]
        public string name;
        public EmployeeRole role;
        public int age = 25;
        public int experience = 0; // Years of experience
        
        [Header("Skills (0-100)")]
        public int programmingSkill = 50;
        public int designSkill = 50;
        public int marketingSkill = 50;
        public int managementSkill = 50;
        
        [Header("Employment")]
        public long salary = 3000; // Monthly salary
        public int happiness = 75; // 0-100, affects productivity
        public bool isWorking = true;
        public string currentProject = "";
        
        [Header("Productivity")]
        public float productivityMultiplier = 1f;
        public int monthsEmployed = 0;
        
        public Employee()
        {
            GenerateRandomEmployee();
        }
        
        public Employee(string employeeName, EmployeeRole employeeRole)
        {
            name = employeeName;
            role = employeeRole;
            GenerateSkillsForRole();
        }
        
        private void GenerateRandomEmployee()
        {
            // Generate random name
            string[] firstNames = { "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>" };
            string[] lastNames = { "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>" };
            
            name = firstNames[Random.Range(0, firstNames.Length)] + " " + lastNames[Random.Range(0, lastNames.Length)];
            
            // Random age and experience
            age = Random.Range(22, 55);
            experience = Random.Range(0, age - 20);
            
            // Random role
            role = (EmployeeRole)Random.Range(0, System.Enum.GetValues(typeof(EmployeeRole)).Length);
            
            GenerateSkillsForRole();
            
            // Calculate salary based on skills and experience
            CalculateSalary();
        }
        
        private void GenerateSkillsForRole()
        {
            // Base skills
            programmingSkill = Random.Range(30, 70);
            designSkill = Random.Range(30, 70);
            marketingSkill = Random.Range(30, 70);
            managementSkill = Random.Range(30, 70);
            
            // Boost skills based on role
            switch (role)
            {
                case EmployeeRole.Programmer:
                    programmingSkill = Random.Range(60, 95);
                    break;
                case EmployeeRole.Designer:
                    designSkill = Random.Range(60, 95);
                    break;
                case EmployeeRole.Marketer:
                    marketingSkill = Random.Range(60, 95);
                    break;
                case EmployeeRole.ProjectManager:
                    managementSkill = Random.Range(60, 95);
                    break;
                case EmployeeRole.CEO:
                    managementSkill = Random.Range(70, 95);
                    marketingSkill = Random.Range(60, 85);
                    break;
            }
            
            // Add experience bonus
            int experienceBonus = experience * 2;
            programmingSkill = Mathf.Min(100, programmingSkill + experienceBonus);
            designSkill = Mathf.Min(100, designSkill + experienceBonus);
            marketingSkill = Mathf.Min(100, marketingSkill + experienceBonus);
            managementSkill = Mathf.Min(100, managementSkill + experienceBonus);
        }
        
        private void CalculateSalary()
        {
            // Base salary
            long baseSalary = 2500;
            
            // Role multiplier
            float roleMultiplier = 1f;
            switch (role)
            {
                case EmployeeRole.Programmer:
                    roleMultiplier = 1.2f;
                    break;
                case EmployeeRole.Designer:
                    roleMultiplier = 1.1f;
                    break;
                case EmployeeRole.Marketer:
                    roleMultiplier = 1.0f;
                    break;
                case EmployeeRole.ProjectManager:
                    roleMultiplier = 1.3f;
                    break;
                case EmployeeRole.CEO:
                    roleMultiplier = 2.0f;
                    break;
            }
            
            // Skill average
            float skillAverage = (programmingSkill + designSkill + marketingSkill + managementSkill) / 4f;
            float skillMultiplier = skillAverage / 50f; // 50 is average skill
            
            // Experience multiplier
            float experienceMultiplier = 1f + (experience * 0.05f);
            
            salary = (long)(baseSalary * roleMultiplier * skillMultiplier * experienceMultiplier);
        }
        
        public float GetProductivity()
        {
            float happinessMultiplier = happiness / 100f;
            return productivityMultiplier * happinessMultiplier;
        }
        
        public void UpdateHappiness(int change)
        {
            happiness = Mathf.Clamp(happiness + change, 0, 100);
        }
        
        public void GainExperience(int months = 1)
        {
            monthsEmployed += months;
            if (monthsEmployed >= 12)
            {
                experience++;
                monthsEmployed = 0;
                
                // Slight skill improvement with experience
                programmingSkill = Mathf.Min(100, programmingSkill + Random.Range(0, 3));
                designSkill = Mathf.Min(100, designSkill + Random.Range(0, 3));
                marketingSkill = Mathf.Min(100, marketingSkill + Random.Range(0, 3));
                managementSkill = Mathf.Min(100, managementSkill + Random.Range(0, 3));
                
                // Recalculate salary
                CalculateSalary();
            }
        }
    }
    
    public enum EmployeeRole
    {
        Programmer,
        Designer,
        Marketer,
        ProjectManager,
        CEO
    }
}
