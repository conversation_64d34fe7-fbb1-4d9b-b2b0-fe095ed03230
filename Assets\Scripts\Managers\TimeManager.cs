using UnityEngine;
using System;

namespace ConsoleTycoon
{
    public class TimeManager : MonoBehaviour
    {
        [Header("Time Settings")]
        public int currentYear = 1980;
        public int currentMonth = 1; // 1-12
        public int currentDay = 1; // 1-30 (simplified)
        public float timeSpeed = 1f;
        
        [Header("Game Time")]
        public float realTimePerGameDay = 2f; // Real seconds per game day
        public float realTimePerGameMonth = 60f; // Real seconds per game month
        public bool useMonthlyProgression = true; // If true, progress by months instead of days
        
        [Header("Events")]
        public UnityEngine.Events.UnityEvent OnDayPassed;
        public UnityEngine.Events.UnityEvent OnMonthPassed;
        public UnityEngine.Events.UnityEvent OnYearPassed;
        
        private float timeAccumulator = 0f;
        private bool isGameRunning = false;
        
        // Properties for easy access
        public string CurrentDateString => $"{GetMonthName(currentMonth)} {currentYear}";
        public string CurrentFullDateString => $"{GetMonthName(currentMonth)} {currentDay}, {currentYear}";
        public int TotalMonths => (currentYear - 1980) * 12 + currentMonth;
        
        private void Start()
        {
            // Initialize events if they're null
            if (OnDayPassed == null) OnDayPassed = new UnityEngine.Events.UnityEvent();
            if (OnMonthPassed == null) OnMonthPassed = new UnityEngine.Events.UnityEvent();
            if (OnYearPassed == null) OnYearPassed = new UnityEngine.Events.UnityEvent();
        }
        
        public void StartGame()
        {
            isGameRunning = true;
            timeAccumulator = 0f;
            Debug.Log($"Game started in {CurrentDateString}");
        }
        
        public void StopGame()
        {
            isGameRunning = false;
        }
        
        public void UpdateTime()
        {
            if (!isGameRunning || GameManager.Instance.isPaused)
                return;
            
            timeAccumulator += Time.deltaTime * timeSpeed;
            
            if (useMonthlyProgression)
            {
                // Progress by months
                if (timeAccumulator >= realTimePerGameMonth)
                {
                    AdvanceMonth();
                    timeAccumulator = 0f;
                }
            }
            else
            {
                // Progress by days
                if (timeAccumulator >= realTimePerGameDay)
                {
                    AdvanceDay();
                    timeAccumulator = 0f;
                }
            }
        }
        
        private void AdvanceDay()
        {
            currentDay++;
            
            // Check if month should advance
            if (currentDay > 30) // Simplified month length
            {
                currentDay = 1;
                AdvanceMonth();
            }
            
            OnDayPassed?.Invoke();
            Debug.Log($"Day advanced to {CurrentFullDateString}");
        }
        
        private void AdvanceMonth()
        {
            currentMonth++;
            
            // Check if year should advance
            if (currentMonth > 12)
            {
                currentMonth = 1;
                AdvanceYear();
            }
            
            // Trigger monthly events
            OnMonthPassed?.Invoke();
            ProcessMonthlyEvents();
            
            Debug.Log($"Month advanced to {CurrentDateString}");
        }
        
        private void AdvanceYear()
        {
            currentYear++;
            OnYearPassed?.Invoke();
            ProcessYearlyEvents();
            
            Debug.Log($"Year advanced to {currentYear}");
        }
        
        private void ProcessMonthlyEvents()
        {
            // Update company finances
            if (GameManager.Instance?.playerCompany != null)
            {
                GameManager.Instance.playerCompany.UpdateMonthlyFinances();
            }
            
            // Update all active projects
            UpdateActiveProjects();
            
            // Update market and sales
            UpdateMarketAndSales();
            
            // Update employee experience
            UpdateEmployees();
        }
        
        private void ProcessYearlyEvents()
        {
            // Annual market changes
            if (GameManager.Instance?.marketManager != null)
            {
                GameManager.Instance.marketManager.ProcessAnnualMarketChanges();
            }
            
            // Technology advancement
            ProcessTechnologyAdvancement();
            
            // Competition updates
            if (GameManager.Instance?.marketManager != null)
            {
                GameManager.Instance.marketManager.UpdateCompetitors();
            }
        }
        
        private void UpdateActiveProjects()
        {
            if (GameManager.Instance?.playerCompany == null) return;
            
            var company = GameManager.Instance.playerCompany;
            
            // Update console projects
            for (int i = company.activeConsoleProjects.Count - 1; i >= 0; i--)
            {
                var project = company.activeConsoleProjects[i];
                var team = GetProjectTeam(project.assignedEmployees);
                project.UpdateProgress(team);
                
                // Remove completed or cancelled projects
                if (project.IsCompleted() || project.IsCancelled())
                {
                    company.activeConsoleProjects.RemoveAt(i);
                }
            }
            
            // Update game projects
            for (int i = company.activeGameProjects.Count - 1; i >= 0; i--)
            {
                var project = company.activeGameProjects[i];
                var team = GetProjectTeam(project.assignedEmployees);
                project.UpdateProgress(team);
                
                // Remove completed or cancelled projects
                if (project.IsCompleted() || project.IsCancelled())
                {
                    company.activeGameProjects.RemoveAt(i);
                }
            }
        }
        
        private void UpdateMarketAndSales()
        {
            if (GameManager.Instance?.playerCompany == null) return;
            
            var company = GameManager.Instance.playerCompany;
            
            // Update console sales
            foreach (var console in company.releasedConsoles)
            {
                console.SimulateMonthlySales();
            }
            
            // Update game sales
            foreach (var game in company.releasedGames)
            {
                game.SimulateMonthlySales();
            }
        }
        
        private void UpdateEmployees()
        {
            if (GameManager.Instance?.playerCompany == null) return;
            
            var company = GameManager.Instance.playerCompany;
            
            foreach (var employee in company.employees)
            {
                employee.GainExperience(1);
                
                // Random happiness changes
                int happinessChange = Random.Range(-2, 3);
                employee.UpdateHappiness(happinessChange);
            }
        }
        
        private void ProcessTechnologyAdvancement()
        {
            // This could be expanded to automatically unlock new technologies over time
            // For now, just a placeholder
            Debug.Log("Processing annual technology advancement...");
        }
        
        private System.Collections.Generic.List<Employee> GetProjectTeam(System.Collections.Generic.List<string> employeeNames)
        {
            var team = new System.Collections.Generic.List<Employee>();
            
            if (GameManager.Instance?.playerCompany?.employees != null)
            {
                foreach (var employeeName in employeeNames)
                {
                    var employee = GameManager.Instance.playerCompany.employees.Find(e => e.name == employeeName);
                    if (employee != null)
                    {
                        team.Add(employee);
                    }
                }
            }
            
            return team;
        }
        
        public void SetTimeSpeed(float speed)
        {
            timeSpeed = Mathf.Clamp(speed, 0.1f, 10f);
        }
        
        public void PauseTime()
        {
            GameManager.Instance.PauseGame();
        }
        
        public void ResumeTime()
        {
            GameManager.Instance.ResumeGame();
        }
        
        public string GetMonthName(int month)
        {
            string[] monthNames = {
                "", "January", "February", "March", "April", "May", "June",
                "July", "August", "September", "October", "November", "December"
            };
            
            if (month >= 1 && month <= 12)
                return monthNames[month];
            
            return "Unknown";
        }
        
        public int GetDaysInMonth(int month, int year)
        {
            // Simplified - all months have 30 days
            return 30;
        }
        
        public bool IsLeapYear(int year)
        {
            return year % 4 == 0 && (year % 100 != 0 || year % 400 == 0);
        }
        
        // Utility methods for date calculations
        public int GetMonthsBetween(int startYear, int startMonth, int endYear, int endMonth)
        {
            return (endYear - startYear) * 12 + (endMonth - startMonth);
        }
        
        public void AdvanceTimeBy(int months)
        {
            for (int i = 0; i < months; i++)
            {
                AdvanceMonth();
            }
        }
        
        public void SetDate(int year, int month, int day = 1)
        {
            currentYear = year;
            currentMonth = Mathf.Clamp(month, 1, 12);
            currentDay = Mathf.Clamp(day, 1, 30);
        }
    }
}
