using UnityEngine;
using System.Collections.Generic;

namespace ConsoleTycoon
{
    [CreateAssetMenu(fileName = "CompanyData", menuName = "Console Tycoon/Company Data")]
    public class CompanyData : ScriptableObject
    {
        [Header("Company Information")]
        public string companyName = "New Gaming Company";
        public string founderName = "Player";
        public int foundingYear = 1980;
        
        [Header("Finances")]
        public long currentMoney = 100000; // Starting with $100,000
        public long totalRevenue = 0;
        public long totalExpenses = 0;
        public long monthlyIncome = 0;
        public long monthlyExpenses = 0;
        
        [Header("Reputation & Market")]
        public float reputation = 50f; // 0-100 scale
        public float marketShare = 0f; // 0-100 percentage
        public int totalConsolesSold = 0;
        public int totalGamesSold = 0;
        
        [Header("Research & Development")]
        public int researchPoints = 0;
        public List<string> unlockedTechnologies = new List<string>();
        
        [Header("Employees")]
        public List<Employee> employees = new List<Employee>();
        public int maxEmployees = 10;
        
        [Header("Current Projects")]
        public List<ConsoleProject> activeConsoleProjects = new List<ConsoleProject>();
        public List<GameProject> activeGameProjects = new List<GameProject>();
        
        [Header("Released Products")]
        public List<Console> releasedConsoles = new List<Console>();
        public List<Game> releasedGames = new List<Game>();

        public void Initialize()
        {
            // Set default starting values
            companyName = "New Gaming Company";
            founderName = "Player";
            foundingYear = 1980;
            currentMoney = 100000;
            reputation = 50f;
            marketShare = 0f;
            
            // Clear lists
            unlockedTechnologies.Clear();
            employees.Clear();
            activeConsoleProjects.Clear();
            activeGameProjects.Clear();
            releasedConsoles.Clear();
            releasedGames.Clear();
            
            // Add starting technologies
            unlockedTechnologies.Add("Basic CPU");
            unlockedTechnologies.Add("Basic Graphics");
            unlockedTechnologies.Add("Basic Sound");
            
            // Add starting employee (the founder)
            Employee founder = new Employee
            {
                name = founderName,
                role = EmployeeRole.CEO,
                programmingSkill = 60,
                designSkill = 60,
                marketingSkill = 60,
                salary = 5000
            };
            employees.Add(founder);
        }
        
        public bool CanAfford(long amount)
        {
            return currentMoney >= amount;
        }
        
        public bool SpendMoney(long amount)
        {
            if (CanAfford(amount))
            {
                currentMoney -= amount;
                totalExpenses += amount;
                return true;
            }
            return false;
        }
        
        public void EarnMoney(long amount)
        {
            currentMoney += amount;
            totalRevenue += amount;
        }
        
        public void UpdateMonthlyFinances()
        {
            // Calculate monthly expenses (salaries, etc.)
            monthlyExpenses = 0;
            foreach (Employee employee in employees)
            {
                monthlyExpenses += employee.salary;
            }
            
            // Deduct monthly expenses
            SpendMoney(monthlyExpenses);
            
            // Calculate monthly income from ongoing sales
            monthlyIncome = CalculateMonthlyIncome();
            EarnMoney(monthlyIncome);
        }
        
        private long CalculateMonthlyIncome()
        {
            long income = 0;
            
            // Income from console sales
            foreach (Console console in releasedConsoles)
            {
                if (console.isActive)
                {
                    income += console.monthlySales * console.price;
                }
            }
            
            // Income from game sales
            foreach (Game game in releasedGames)
            {
                if (game.isActive)
                {
                    income += game.monthlySales * game.price;
                }
            }
            
            return income;
        }
        
        public void AddEmployee(Employee employee)
        {
            if (employees.Count < maxEmployees)
            {
                employees.Add(employee);
            }
        }
        
        public void RemoveEmployee(Employee employee)
        {
            employees.Remove(employee);
        }
        
        public bool HasTechnology(string technologyName)
        {
            return unlockedTechnologies.Contains(technologyName);
        }
        
        public void UnlockTechnology(string technologyName)
        {
            if (!HasTechnology(technologyName))
            {
                unlockedTechnologies.Add(technologyName);
            }
        }
    }
}
