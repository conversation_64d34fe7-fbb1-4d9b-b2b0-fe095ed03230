using UnityEngine;
using System.Collections.Generic;

namespace ConsoleTycoon
{
    [System.Serializable]
    public class GameProject
    {
        [Header("Project Information")]
        public string projectName;
        public string gameTitle;
        public GameGenre genre;
        public string targetConsole;
        public ProjectPhase currentPhase = ProjectPhase.Planning;
        public float progressPercentage = 0f;
        
        [Header("Timeline")]
        public int totalDevelopmentTime = 6; // Months
        public int monthsSpent = 0;
        public int estimatedMonthsRemaining = 6;
        
        [Header("Budget")]
        public long totalBudget = 100000;
        public long spentBudget = 0;
        public long monthlyBurnRate = 15000;
        
        [Header("Team")]
        public List<string> assignedEmployees = new List<string>();
        public int requiredProgrammers = 2;
        public int requiredDesigners = 1;
        
        [Header("Game Features")]
        public List<string> plannedFeatures = new List<string>();
        public List<string> completedFeatures = new List<string>();
        public bool isMultiplayer = false;
        public bool hasOnlineFeatures = false;
        public int targetPlaytime = 10; // Hours
        
        [Header("Quality Metrics")]
        public float gameplayQuality = 0f;
        public float graphicsQuality = 0f;
        public float soundQuality = 0f;
        public float storyQuality = 0f;
        public float overallQuality = 0f;
        
        [Header("Market Research")]
        public long targetPrice = 60;
        public long marketingBudget = 20000;
        public float marketDemand = 50f; // 0-100
        
        [Header("Risks & Issues")]
        public List<string> currentIssues = new List<string>();
        public float riskLevel = 0f;
        
        public GameProject()
        {
            Initialize();
        }
        
        public GameProject(string title, GameGenre gameGenre, string console)
        {
            gameTitle = title;
            projectName = $"{title} Development";
            genre = gameGenre;
            targetConsole = console;
            Initialize();
        }
        
        private void Initialize()
        {
            assignedEmployees = new List<string>();
            plannedFeatures = new List<string>();
            completedFeatures = new List<string>();
            currentIssues = new List<string>();
            
            // Set development time based on genre
            SetDevelopmentTimeByGenre();
            
            // Add basic planned features
            plannedFeatures.Add("Core Gameplay");
            plannedFeatures.Add("Basic Graphics");
            plannedFeatures.Add("Sound Effects");
            plannedFeatures.Add("User Interface");
        }
        
        private void SetDevelopmentTimeByGenre()
        {
            switch (genre)
            {
                case GameGenre.Puzzle:
                    totalDevelopmentTime = 3;
                    totalBudget = 50000;
                    break;
                case GameGenre.Platformer:
                    totalDevelopmentTime = 4;
                    totalBudget = 75000;
                    break;
                case GameGenre.Action:
                    totalDevelopmentTime = 6;
                    totalBudget = 100000;
                    break;
                case GameGenre.Sports:
                case GameGenre.Racing:
                    totalDevelopmentTime = 8;
                    totalBudget = 150000;
                    break;
                case GameGenre.RPG:
                    totalDevelopmentTime = 12;
                    totalBudget = 200000;
                    targetPlaytime = 40;
                    break;
                case GameGenre.Strategy:
                    totalDevelopmentTime = 10;
                    totalBudget = 175000;
                    break;
                default:
                    totalDevelopmentTime = 6;
                    totalBudget = 100000;
                    break;
            }
            
            estimatedMonthsRemaining = totalDevelopmentTime;
            monthlyBurnRate = totalBudget / totalDevelopmentTime;
        }
        
        public void UpdateProgress(List<Employee> team)
        {
            if (currentPhase == ProjectPhase.Completed || currentPhase == ProjectPhase.Cancelled)
                return;
            
            // Calculate monthly progress
            float monthlyProgress = CalculateMonthlyProgress(team);
            
            // Apply progress
            progressPercentage += monthlyProgress;
            monthsSpent++;
            spentBudget += monthlyBurnRate;
            
            // Update estimated time remaining
            if (monthlyProgress > 0)
            {
                estimatedMonthsRemaining = Mathf.RoundToInt((100f - progressPercentage) / monthlyProgress);
            }
            
            // Check for phase transitions
            CheckPhaseTransition();
            
            // Update quality metrics
            UpdateQualityMetrics(team);
            
            // Check for issues
            CheckForIssues();
            
            // Complete features
            CompleteFeatures();
            
            // Check if project is complete
            if (progressPercentage >= 100f)
            {
                CompleteProject();
            }
        }
        
        private float CalculateMonthlyProgress(List<Employee> team)
        {
            if (team == null || team.Count == 0)
                return 0f;
            
            float baseProgress = 100f / totalDevelopmentTime;
            
            // Team efficiency
            float teamEfficiency = CalculateTeamEfficiency(team);
            
            // Genre complexity modifier
            float complexityModifier = GetGenreComplexityModifier();
            
            // Budget constraint
            float budgetModifier = 1f;
            if (spentBudget >= totalBudget * 0.9f)
            {
                budgetModifier = 0.6f;
            }
            
            return baseProgress * teamEfficiency * complexityModifier * budgetModifier;
        }
        
        private float CalculateTeamEfficiency(List<Employee> team)
        {
            float efficiency = 0f;
            int programmers = 0, designers = 0;
            
            foreach (Employee employee in team)
            {
                switch (employee.role)
                {
                    case EmployeeRole.Programmer:
                        programmers++;
                        efficiency += employee.programmingSkill * employee.GetProductivity();
                        break;
                    case EmployeeRole.Designer:
                        designers++;
                        efficiency += employee.designSkill * employee.GetProductivity();
                        break;
                    case EmployeeRole.ProjectManager:
                        efficiency += employee.managementSkill * employee.GetProductivity() * 0.5f;
                        break;
                }
            }
            
            // Team composition penalties
            float compositionPenalty = 1f;
            if (programmers < requiredProgrammers) compositionPenalty *= 0.7f;
            if (designers < requiredDesigners) compositionPenalty *= 0.8f;
            
            // Normalize efficiency
            efficiency = (efficiency / (team.Count * 50f)) * compositionPenalty;
            
            return Mathf.Clamp(efficiency, 0.1f, 2f);
        }
        
        private float GetGenreComplexityModifier()
        {
            switch (genre)
            {
                case GameGenre.Puzzle: return 1.3f;
                case GameGenre.Platformer: return 1.1f;
                case GameGenre.Action: return 1.0f;
                case GameGenre.Sports: return 0.9f;
                case GameGenre.Racing: return 0.9f;
                case GameGenre.RPG: return 0.7f;
                case GameGenre.Strategy: return 0.8f;
                default: return 1.0f;
            }
        }
        
        private void CheckPhaseTransition()
        {
            switch (currentPhase)
            {
                case ProjectPhase.Planning:
                    if (progressPercentage >= 20f) currentPhase = ProjectPhase.Design;
                    break;
                case ProjectPhase.Design:
                    if (progressPercentage >= 40f) currentPhase = ProjectPhase.Development;
                    break;
                case ProjectPhase.Development:
                    if (progressPercentage >= 80f) currentPhase = ProjectPhase.Testing;
                    break;
                case ProjectPhase.Testing:
                    if (progressPercentage >= 100f) currentPhase = ProjectPhase.Completed;
                    break;
            }
        }
        
        private void UpdateQualityMetrics(List<Employee> team)
        {
            if (team == null || team.Count == 0) return;
            
            float avgProgramming = 0f;
            float avgDesign = 0f;
            
            foreach (Employee employee in team)
            {
                avgProgramming += employee.programmingSkill;
                avgDesign += employee.designSkill;
            }
            
            avgProgramming /= team.Count;
            avgDesign /= team.Count;
            
            // Update quality based on current phase
            switch (currentPhase)
            {
                case ProjectPhase.Planning:
                case ProjectPhase.Design:
                    gameplayQuality = (avgProgramming * 0.7f + avgDesign * 0.3f) + Random.Range(-5f, 5f);
                    storyQuality = avgDesign + Random.Range(-5f, 5f);
                    break;
                case ProjectPhase.Development:
                    graphicsQuality = avgDesign + Random.Range(-5f, 5f);
                    soundQuality = avgDesign * 0.8f + Random.Range(-5f, 5f);
                    break;
            }
            
            overallQuality = (gameplayQuality + graphicsQuality + soundQuality + storyQuality) / 4f;
        }
        
        private void CheckForIssues()
        {
            currentIssues.Clear();
            riskLevel = 0f;
            
            // Budget issues
            if (spentBudget >= totalBudget * 0.8f)
            {
                currentIssues.Add("Budget running low");
                riskLevel += 25f;
            }
            
            // Timeline issues
            if (monthsSpent > totalDevelopmentTime * 0.8f && progressPercentage < 80f)
            {
                currentIssues.Add("Behind schedule");
                riskLevel += 30f;
            }
            
            // Team issues
            if (assignedEmployees.Count < (requiredProgrammers + requiredDesigners))
            {
                currentIssues.Add("Understaffed");
                riskLevel += 20f;
            }
            
            // Quality issues
            if (overallQuality < 40f)
            {
                currentIssues.Add("Quality concerns");
                riskLevel += 25f;
            }
            
            riskLevel = Mathf.Clamp(riskLevel, 0f, 100f);
        }
        
        private void CompleteFeatures()
        {
            // Move features from planned to completed based on progress
            int featuresToComplete = Mathf.FloorToInt(progressPercentage / 25f);
            
            while (completedFeatures.Count < featuresToComplete && plannedFeatures.Count > 0)
            {
                string feature = plannedFeatures[0];
                plannedFeatures.RemoveAt(0);
                completedFeatures.Add(feature);
            }
        }
        
        private void CompleteProject()
        {
            currentPhase = ProjectPhase.Completed;
            progressPercentage = 100f;
            
            // Create the final game
            Game newGame = CreateFinalGame();
            
            // Add to company's released games
            if (GameManager.Instance?.playerCompany != null)
            {
                GameManager.Instance.playerCompany.releasedGames.Add(newGame);
            }
        }
        
        private Game CreateFinalGame()
        {
            Game game = new Game(gameTitle, genre, targetConsole);
            
            // Apply quality metrics
            game.gameplayQuality = gameplayQuality;
            game.graphicsQuality = graphicsQuality;
            game.soundQuality = soundQuality;
            game.storyQuality = storyQuality;
            game.overallQuality = overallQuality;
            
            // Apply features
            game.features = new List<string>(completedFeatures);
            game.isMultiplayer = isMultiplayer;
            game.hasOnlineFeatures = hasOnlineFeatures;
            game.estimatedPlaytime = targetPlaytime;
            
            // Set costs and pricing
            game.developmentCost = spentBudget;
            game.marketingBudget = marketingBudget;
            game.price = targetPrice;
            
            return game;
        }
        
        public void CancelProject()
        {
            currentPhase = ProjectPhase.Cancelled;
        }
        
        public bool IsCompleted()
        {
            return currentPhase == ProjectPhase.Completed;
        }
        
        public bool IsCancelled()
        {
            return currentPhase == ProjectPhase.Cancelled;
        }
    }
}
