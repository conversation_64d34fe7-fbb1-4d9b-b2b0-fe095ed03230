using UnityEngine;
using System.Collections.Generic;

namespace ConsoleTycoon
{
    public class ConsoleManager : MonoBehaviour
    {
        [Header("Console Development")]
        public List<ConsoleTechnology> availableTechnologies = new List<ConsoleTechnology>();
        public List<ConsoleTemplate> consoleTemplates = new List<ConsoleTemplate>();
        
        [Header("Market Data")]
        public List<MarketTrend> currentMarketTrends = new List<MarketTrend>();
        public int currentGeneration = 1;
        public int[] generationYears = { 1980, 1985, 1990, 1995, 2000, 2005, 2010, 2015, 2020 };
        
        private CompanyData playerCompany;
        
        private void Start()
        {
            playerCompany = GameManager.Instance?.playerCompany;
        }
        
        public void Initialize()
        {
            InitializeTechnologies();
            InitializeConsoleTemplates();
            UpdateCurrentGeneration();
            
            Debug.Log("Console Manager initialized");
        }
        
        private void InitializeTechnologies()
        {
            availableTechnologies.Clear();
            
            // CPU Technologies
            availableTechnologies.Add(new ConsoleTechnology("8-bit CPU", TechnologyType.CPU, 1980, 10, 50000));
            availableTechnologies.Add(new ConsoleTechnology("16-bit CPU", TechnologyType.CPU, 1985, 25, 100000));
            availableTechnologies.Add(new ConsoleTechnology("32-bit CPU", TechnologyType.CPU, 1990, 40, 200000));
            availableTechnologies.Add(new ConsoleTechnology("64-bit CPU", TechnologyType.CPU, 1995, 60, 400000));
            availableTechnologies.Add(new ConsoleTechnology("Multi-core CPU", TechnologyType.CPU, 2000, 80, 800000));
            
            // Graphics Technologies
            availableTechnologies.Add(new ConsoleTechnology("Basic Graphics", TechnologyType.Graphics, 1980, 15, 30000));
            availableTechnologies.Add(new ConsoleTechnology("Sprite Graphics", TechnologyType.Graphics, 1983, 25, 60000));
            availableTechnologies.Add(new ConsoleTechnology("2D Acceleration", TechnologyType.Graphics, 1988, 35, 120000));
            availableTechnologies.Add(new ConsoleTechnology("3D Graphics", TechnologyType.Graphics, 1993, 50, 250000));
            availableTechnologies.Add(new ConsoleTechnology("Advanced 3D", TechnologyType.Graphics, 1998, 70, 500000));
            availableTechnologies.Add(new ConsoleTechnology("HD Graphics", TechnologyType.Graphics, 2003, 85, 1000000));
            
            // Sound Technologies
            availableTechnologies.Add(new ConsoleTechnology("Basic Sound", TechnologyType.Sound, 1980, 10, 20000));
            availableTechnologies.Add(new ConsoleTechnology("Stereo Sound", TechnologyType.Sound, 1985, 20, 40000));
            availableTechnologies.Add(new ConsoleTechnology("CD Audio", TechnologyType.Sound, 1990, 35, 80000));
            availableTechnologies.Add(new ConsoleTechnology("Surround Sound", TechnologyType.Sound, 1995, 50, 160000));
            availableTechnologies.Add(new ConsoleTechnology("Digital Audio", TechnologyType.Sound, 2000, 70, 320000));
            
            // Memory Technologies
            availableTechnologies.Add(new ConsoleTechnology("Basic RAM", TechnologyType.Memory, 1980, 10, 25000));
            availableTechnologies.Add(new ConsoleTechnology("Extended RAM", TechnologyType.Memory, 1985, 25, 50000));
            availableTechnologies.Add(new ConsoleTechnology("High-Speed RAM", TechnologyType.Memory, 1990, 40, 100000));
            availableTechnologies.Add(new ConsoleTechnology("Advanced RAM", TechnologyType.Memory, 1995, 60, 200000));
            availableTechnologies.Add(new ConsoleTechnology("DDR RAM", TechnologyType.Memory, 2000, 80, 400000));
            
            // Storage Technologies
            availableTechnologies.Add(new ConsoleTechnology("Cartridge", TechnologyType.Storage, 1980, 15, 15000));
            availableTechnologies.Add(new ConsoleTechnology("Floppy Disk", TechnologyType.Storage, 1985, 25, 30000));
            availableTechnologies.Add(new ConsoleTechnology("CD-ROM", TechnologyType.Storage, 1990, 40, 60000));
            availableTechnologies.Add(new ConsoleTechnology("DVD", TechnologyType.Storage, 1995, 60, 120000));
            availableTechnologies.Add(new ConsoleTechnology("Blu-ray", TechnologyType.Storage, 2000, 80, 240000));
            
            // Feature Technologies
            availableTechnologies.Add(new ConsoleTechnology("Network Adapter", TechnologyType.Feature, 1995, 30, 100000));
            availableTechnologies.Add(new ConsoleTechnology("Online Services", TechnologyType.Feature, 2000, 50, 200000));
            availableTechnologies.Add(new ConsoleTechnology("Backwards Compatibility", TechnologyType.Feature, 1990, 40, 150000));
            availableTechnologies.Add(new ConsoleTechnology("Motion Controls", TechnologyType.Feature, 2005, 60, 300000));
        }
        
        private void InitializeConsoleTemplates()
        {
            consoleTemplates.Clear();
            
            // Budget Console Template
            consoleTemplates.Add(new ConsoleTemplate
            {
                name = "Budget Console",
                description = "A low-cost console for casual gamers",
                baseCost = 500000,
                targetPrice = 199,
                developmentTime = 18,
                requiredTechnologies = new List<string> { "8-bit CPU", "Basic Graphics", "Basic Sound" }
            });
            
            // Standard Console Template
            consoleTemplates.Add(new ConsoleTemplate
            {
                name = "Standard Console",
                description = "A mid-range console for mainstream gaming",
                baseCost = 1000000,
                targetPrice = 299,
                developmentTime = 24,
                requiredTechnologies = new List<string> { "16-bit CPU", "Sprite Graphics", "Stereo Sound" }
            });
            
            // Premium Console Template
            consoleTemplates.Add(new ConsoleTemplate
            {
                name = "Premium Console",
                description = "A high-end console for enthusiast gamers",
                baseCost = 2000000,
                targetPrice = 399,
                developmentTime = 30,
                requiredTechnologies = new List<string> { "32-bit CPU", "3D Graphics", "CD Audio" }
            });
        }
        
        public ConsoleProject StartConsoleProject(string consoleName, ConsoleTemplate template = null)
        {
            if (playerCompany == null) return null;
            
            ConsoleProject project = new ConsoleProject(consoleName + " Project", consoleName);
            
            if (template != null)
            {
                ApplyTemplate(project, template);
            }
            else
            {
                // Default project settings
                project.totalBudget = 1000000;
                project.totalDevelopmentTime = 24;
            }
            
            playerCompany.activeConsoleProjects.Add(project);
            
            Debug.Log($"Started console project: {project.projectName}");
            return project;
        }
        
        private void ApplyTemplate(ConsoleProject project, ConsoleTemplate template)
        {
            project.totalBudget = template.baseCost;
            project.totalDevelopmentTime = template.developmentTime;
            project.monthlyBurnRate = template.baseCost / template.developmentTime;
            
            // Set target specifications based on template
            foreach (string techName in template.requiredTechnologies)
            {
                var tech = GetTechnology(techName);
                if (tech != null && IsTechnologyAvailable(tech))
                {
                    ApplyTechnologyToProject(project, tech);
                }
            }
        }
        
        public bool IsTechnologyAvailable(ConsoleTechnology technology)
        {
            if (GameManager.Instance?.timeManager == null) return false;
            
            int currentYear = GameManager.Instance.timeManager.currentYear;
            
            // Check if technology is available in current year
            if (currentYear < technology.availableYear) return false;
            
            // Check if player has unlocked this technology
            if (playerCompany != null)
            {
                return playerCompany.HasTechnology(technology.name);
            }
            
            return false;
        }
        
        public bool ResearchTechnology(string technologyName)
        {
            var technology = GetTechnology(technologyName);
            if (technology == null) return false;
            
            if (!CanResearchTechnology(technology)) return false;
            
            // Pay research cost
            if (GameManager.Instance?.financeManager?.SpendMoney(technology.researchCost, 
                $"Research: {technology.name}", TransactionType.Expense) == true)
            {
                playerCompany?.UnlockTechnology(technology.name);
                Debug.Log($"Researched technology: {technology.name}");
                return true;
            }
            
            return false;
        }
        
        public bool CanResearchTechnology(ConsoleTechnology technology)
        {
            if (playerCompany == null) return false;
            
            // Check if already researched
            if (playerCompany.HasTechnology(technology.name)) return false;
            
            // Check if available in current year
            if (!IsTechnologyAvailable(technology)) return false;
            
            // Check if can afford
            return GameManager.Instance?.financeManager?.CanAfford(technology.researchCost) ?? false;
        }
        
        public ConsoleTechnology GetTechnology(string name)
        {
            return availableTechnologies.Find(t => t.name == name);
        }
        
        public List<ConsoleTechnology> GetAvailableTechnologies(TechnologyType type = TechnologyType.All)
        {
            var available = new List<ConsoleTechnology>();
            
            foreach (var tech in availableTechnologies)
            {
                if ((type == TechnologyType.All || tech.type == type) && IsTechnologyAvailable(tech))
                {
                    available.Add(tech);
                }
            }
            
            return available;
        }
        
        public void ApplyTechnologyToProject(ConsoleProject project, ConsoleTechnology technology)
        {
            switch (technology.type)
            {
                case TechnologyType.CPU:
                    project.targetCpuPower = Mathf.Max(project.targetCpuPower, technology.powerRating);
                    break;
                case TechnologyType.Graphics:
                    project.targetGraphicsPower = Mathf.Max(project.targetGraphicsPower, technology.powerRating);
                    break;
                case TechnologyType.Sound:
                    project.targetSoundQuality = Mathf.Max(project.targetSoundQuality, technology.powerRating);
                    break;
                case TechnologyType.Memory:
                    project.targetMemorySize = Mathf.Max(project.targetMemorySize, technology.powerRating);
                    break;
                case TechnologyType.Storage:
                    project.targetStorageCapacity = Mathf.Max(project.targetStorageCapacity, technology.powerRating);
                    break;
                case TechnologyType.Feature:
                    if (!project.plannedFeatures.Contains(technology.name))
                    {
                        project.plannedFeatures.Add(technology.name);
                    }
                    break;
            }
            
            // Increase project cost and time
            project.totalBudget += technology.researchCost / 10; // 10% of research cost
            project.totalDevelopmentTime += 1; // 1 month per technology
        }
        
        private void UpdateCurrentGeneration()
        {
            if (GameManager.Instance?.timeManager == null) return;
            
            int currentYear = GameManager.Instance.timeManager.currentYear;
            
            for (int i = generationYears.Length - 1; i >= 0; i--)
            {
                if (currentYear >= generationYears[i])
                {
                    currentGeneration = i + 1;
                    break;
                }
            }
        }
        
        public int GetCurrentGeneration()
        {
            UpdateCurrentGeneration();
            return currentGeneration;
        }
        
        public List<ConsoleTechnology> GetTechnologiesForGeneration(int generation)
        {
            var technologies = new List<ConsoleTechnology>();
            
            if (generation <= 0 || generation > generationYears.Length) return technologies;
            
            int generationYear = generationYears[generation - 1];
            int nextGenerationYear = generation < generationYears.Length ? 
                generationYears[generation] : int.MaxValue;
            
            foreach (var tech in availableTechnologies)
            {
                if (tech.availableYear >= generationYear && tech.availableYear < nextGenerationYear)
                {
                    technologies.Add(tech);
                }
            }
            
            return technologies;
        }
        
        public void LaunchConsole(Console console)
        {
            if (console == null) return;
            
            console.Launch();
            
            // Add to market competition
            if (GameManager.Instance?.marketManager != null)
            {
                GameManager.Instance.marketManager.AddPlayerConsoleToMarket(console);
            }
            
            Debug.Log($"Launched console: {console.name}");
        }
        
        public void DiscontinueConsole(Console console)
        {
            if (console == null) return;
            
            console.Discontinue();
            Debug.Log($"Discontinued console: {console.name}");
        }
    }
    
    [System.Serializable]
    public class ConsoleTechnology
    {
        public string name;
        public TechnologyType type;
        public int availableYear;
        public int powerRating; // 0-100 scale
        public long researchCost;
        
        public ConsoleTechnology(string techName, TechnologyType techType, int year, int power, long cost)
        {
            name = techName;
            type = techType;
            availableYear = year;
            powerRating = power;
            researchCost = cost;
        }
    }
    
    [System.Serializable]
    public class ConsoleTemplate
    {
        public string name;
        public string description;
        public long baseCost;
        public long targetPrice;
        public int developmentTime;
        public List<string> requiredTechnologies = new List<string>();
    }
    
    [System.Serializable]
    public class MarketTrend
    {
        public string name;
        public string description;
        public float impactMultiplier;
        public int startYear;
        public int endYear;
    }
    
    public enum TechnologyType
    {
        All,
        CPU,
        Graphics,
        Sound,
        Memory,
        Storage,
        Feature
    }
}
