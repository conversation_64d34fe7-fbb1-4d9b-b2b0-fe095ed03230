using UnityEngine;

namespace ConsoleTycoon
{
    public class GameInitializer : MonoBeh<PERSON>our
    {
        [Header("Manager References")]
        public GameManager gameManager;
        public TimeManager timeManager;
        public FinanceManager financeManager;
        public ConsoleManager consoleManager;
        public GameDevManager gameDevManager;
        public MarketManager marketManager;
        public UIManager uiManager;
        
        [Header("Auto-Setup")]
        public bool autoFindManagers = true;
        public bool initializeOnStart = true;
        
        private void Start()
        {
            if (initializeOnStart)
            {
                InitializeGame();
            }
        }
        
        public void InitializeGame()
        {
            Debug.Log("Initializing Console Tycoon Game...");
            
            // Find managers if auto-setup is enabled
            if (autoFindManagers)
            {
                FindManagers();
            }
            
            // Validate all managers are present
            if (!ValidateManagers())
            {
                Debug.LogError("Not all required managers are present! Game initialization failed.");
                return;
            }
            
            // Set up manager references in GameManager
            SetupGameManagerReferences();
            
            // Initialize all systems in the correct order
            InitializeManagers();
            
            Debug.Log("Console Tycoon Game initialized successfully!");
        }
        
        private void FindManagers()
        {
            if (gameManager == null)
                gameManager = FindObjectOfType<GameManager>();
            
            if (timeManager == null)
                timeManager = FindObjectOfType<TimeManager>();
            
            if (financeManager == null)
                financeManager = FindObjectOfType<FinanceManager>();
            
            if (consoleManager == null)
                consoleManager = FindObjectOfType<ConsoleManager>();
            
            if (gameDevManager == null)
                gameDevManager = FindObjectOfType<GameDevManager>();
            
            if (marketManager == null)
                marketManager = FindObjectOfType<MarketManager>();
            
            if (uiManager == null)
                uiManager = FindObjectOfType<UIManager>();
        }
        
        private bool ValidateManagers()
        {
            bool allPresent = true;
            
            if (gameManager == null)
            {
                Debug.LogError("GameManager not found!");
                allPresent = false;
            }
            
            if (timeManager == null)
            {
                Debug.LogError("TimeManager not found!");
                allPresent = false;
            }
            
            if (financeManager == null)
            {
                Debug.LogError("FinanceManager not found!");
                allPresent = false;
            }
            
            if (consoleManager == null)
            {
                Debug.LogError("ConsoleManager not found!");
                allPresent = false;
            }
            
            if (gameDevManager == null)
            {
                Debug.LogError("GameDevManager not found!");
                allPresent = false;
            }
            
            if (marketManager == null)
            {
                Debug.LogError("MarketManager not found!");
                allPresent = false;
            }
            
            if (uiManager == null)
            {
                Debug.LogWarning("UIManager not found! UI functionality will be limited.");
                // UI Manager is not critical for core functionality
            }
            
            return allPresent;
        }
        
        private void SetupGameManagerReferences()
        {
            gameManager.timeManager = timeManager;
            gameManager.financeManager = financeManager;
            gameManager.consoleManager = consoleManager;
            gameManager.gameDevManager = gameDevManager;
            gameManager.marketManager = marketManager;
            gameManager.uiManager = uiManager;
        }
        
        private void InitializeManagers()
        {
            // Initialize in dependency order
            
            // 1. Initialize GameManager first (creates company data)
            // GameManager initializes automatically in Awake()
            
            // 2. Initialize FinanceManager
            financeManager.StartNewGame();
            
            // 3. Initialize TimeManager
            timeManager.StartGame();
            
            // 4. Initialize ConsoleManager
            consoleManager.Initialize();
            
            // 5. Initialize GameDevManager
            gameDevManager.Initialize();
            
            // 6. Initialize MarketManager
            marketManager.Initialize();
            
            // 7. UI Manager initializes automatically
            
            Debug.Log("All managers initialized successfully!");
        }
        
        [ContextMenu("Create Manager GameObject")]
        public void CreateManagerGameObject()
        {
            // Create a GameObject with all managers attached
            GameObject managerObject = new GameObject("Game Managers");
            
            // Add all manager components
            managerObject.AddComponent<GameManager>();
            managerObject.AddComponent<TimeManager>();
            managerObject.AddComponent<FinanceManager>();
            managerObject.AddComponent<ConsoleManager>();
            managerObject.AddComponent<GameDevManager>();
            managerObject.AddComponent<MarketManager>();
            
            // Set references
            gameManager = managerObject.GetComponent<GameManager>();
            timeManager = managerObject.GetComponent<TimeManager>();
            financeManager = managerObject.GetComponent<FinanceManager>();
            consoleManager = managerObject.GetComponent<ConsoleManager>();
            gameDevManager = managerObject.GetComponent<GameDevManager>();
            marketManager = managerObject.GetComponent<MarketManager>();
            
            Debug.Log("Manager GameObject created with all components!");
        }
        
        [ContextMenu("Test Game Flow")]
        public void TestGameFlow()
        {
            if (!Application.isPlaying)
            {
                Debug.LogWarning("Test Game Flow can only be run in Play Mode!");
                return;
            }
            
            Debug.Log("Testing basic game flow...");
            
            // Test console development
            var consoleProject = consoleManager.StartConsoleProject("Test Console");
            if (consoleProject != null)
            {
                Debug.Log($"Console project started: {consoleProject.projectName}");
            }
            
            // Test game development
            var gameProject = gameDevManager.StartGameProject("Test Game", GameGenre.Action, "Test Console");
            if (gameProject != null)
            {
                Debug.Log($"Game project started: {gameProject.projectName}");
            }
            
            // Test financial transaction
            bool canAfford = financeManager.CanAfford(50000);
            Debug.Log($"Can afford $50,000: {canAfford}");
            
            if (canAfford)
            {
                bool spent = financeManager.SpendMoney(50000, "Test expense");
                Debug.Log($"Spent money successfully: {spent}");
            }
            
            Debug.Log("Game flow test completed!");
        }
        
        private void OnValidate()
        {
            // Auto-find managers in editor
            if (autoFindManagers && !Application.isPlaying)
            {
                FindManagers();
            }
        }
    }
}
