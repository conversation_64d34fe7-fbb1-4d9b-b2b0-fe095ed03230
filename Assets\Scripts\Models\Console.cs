using UnityEngine;
using System.Collections.Generic;

namespace ConsoleTycoon
{
    [System.Serializable]
    public class Console
    {
        [Header("Basic Information")]
        public string name;
        public string description;
        public int releaseYear;
        public int generation; // 1st gen, 2nd gen, etc.
        
        [Header("Technical Specifications")]
        public int cpuPower = 50; // 0-100 scale
        public int graphicsPower = 50;
        public int soundQuality = 50;
        public int memorySize = 50;
        public int storageCapacity = 50;
        
        [Header("Features")]
        public List<string> features = new List<string>();
        public bool hasOnlineCapability = false;
        public bool hasBackwardsCompatibility = false;
        public int controllerPorts = 2;
        
        [Header("Market Information")]
        public long developmentCost = 0;
        public long marketingBudget = 0;
        public long price = 299;
        public int totalUnitsSold = 0;
        public int monthlySales = 0;
        public bool isActive = false; // Still being sold
        
        [Header("Quality & Reception")]
        public float overallQuality = 0f; // Calculated from specs and features
        public float marketReception = 0f; // How well received by market
        public float criticScore = 0f; // Professional reviews
        public float userScore = 0f; // User reviews
        
        [Header("Competition")]
        public List<string> competitorConsoles = new List<string>();
        public float marketSharePercentage = 0f;
        
        [Header("Development")]
        public ConsoleProject developmentProject;
        
        public Console()
        {
            Initialize();
        }
        
        public Console(string consoleName)
        {
            name = consoleName;
            Initialize();
        }
        
        private void Initialize()
        {
            features = new List<string>();
            competitorConsoles = new List<string>();
            releaseYear = GameManager.Instance?.timeManager?.currentYear ?? 1980;
        }
        
        public void CalculateOverallQuality()
        {
            // Calculate quality based on technical specs
            float techScore = (cpuPower + graphicsPower + soundQuality + memorySize + storageCapacity) / 5f;
            
            // Feature bonus
            float featureBonus = features.Count * 5f;
            if (hasOnlineCapability) featureBonus += 10f;
            if (hasBackwardsCompatibility) featureBonus += 8f;
            
            // Controller ports bonus
            featureBonus += (controllerPorts - 1) * 2f;
            
            overallQuality = Mathf.Clamp(techScore + featureBonus, 0f, 100f);
        }
        
        public void CalculateMarketReception()
        {
            // Base reception from quality
            marketReception = overallQuality;
            
            // Price factor (lower price = better reception, but not too low)
            float priceScore = 100f;
            if (price > 400) priceScore -= (price - 400) * 0.1f;
            else if (price < 200) priceScore -= (200 - price) * 0.05f;
            
            marketReception = (marketReception + priceScore) / 2f;
            
            // Marketing budget factor
            float marketingFactor = Mathf.Sqrt(marketingBudget / 10000f);
            marketReception += marketingFactor;
            
            // Competition factor
            float competitionPenalty = competitorConsoles.Count * 5f;
            marketReception -= competitionPenalty;
            
            marketReception = Mathf.Clamp(marketReception, 0f, 100f);
        }
        
        public void SimulateMonthlySales()
        {
            if (!isActive) return;
            
            // Base sales from market reception
            float baseSales = marketReception * 1000f;
            
            // Random factor
            float randomFactor = Random.Range(0.8f, 1.2f);
            
            // Age factor (older consoles sell less)
            int age = (GameManager.Instance?.timeManager?.currentYear ?? releaseYear) - releaseYear;
            float ageFactor = Mathf.Max(0.1f, 1f - (age * 0.1f));
            
            // Market saturation (if too many sold, sales decrease)
            float saturationFactor = 1f;
            if (totalUnitsSold > 1000000)
            {
                saturationFactor = Mathf.Max(0.3f, 1f - (totalUnitsSold / 10000000f));
            }
            
            monthlySales = Mathf.RoundToInt(baseSales * randomFactor * ageFactor * saturationFactor);
            totalUnitsSold += monthlySales;
            
            // Update market share
            UpdateMarketShare();
        }
        
        private void UpdateMarketShare()
        {
            // This would be calculated by the MarketManager
            // For now, just a placeholder
            marketSharePercentage = Mathf.Min(50f, totalUnitsSold / 100000f);
        }
        
        public void AddFeature(string feature)
        {
            if (!features.Contains(feature))
            {
                features.Add(feature);
                CalculateOverallQuality();
            }
        }
        
        public void RemoveFeature(string feature)
        {
            if (features.Contains(feature))
            {
                features.Remove(feature);
                CalculateOverallQuality();
            }
        }
        
        public bool HasFeature(string feature)
        {
            return features.Contains(feature);
        }
        
        public void Launch()
        {
            isActive = true;
            CalculateOverallQuality();
            CalculateMarketReception();
            
            // Generate initial reviews
            criticScore = overallQuality + Random.Range(-15f, 15f);
            criticScore = Mathf.Clamp(criticScore, 0f, 100f);
            
            userScore = marketReception + Random.Range(-10f, 10f);
            userScore = Mathf.Clamp(userScore, 0f, 100f);
        }
        
        public void Discontinue()
        {
            isActive = false;
            monthlySales = 0;
        }
        
        public long GetTotalRevenue()
        {
            return totalUnitsSold * price;
        }
        
        public long GetTotalProfit()
        {
            return GetTotalRevenue() - developmentCost - marketingBudget;
        }
    }
}
