using UnityEngine;
using System.Collections.Generic;

namespace ConsoleTycoon
{
    [System.Serializable]
    public class Game
    {
        [Header("Basic Information")]
        public string title;
        public string description;
        public GameGenre genre;
        public int releaseYear;
        public string targetConsole; // Which console this game is for
        
        [Header("Development")]
        public int developmentTime = 6; // Months
        public long developmentCost = 0;
        public long marketingBudget = 0;
        public List<string> developmentTeam = new List<string>(); // Employee names
        
        [Header("Quality Metrics")]
        public float gameplayQuality = 0f; // 0-100
        public float graphicsQuality = 0f;
        public float soundQuality = 0f;
        public float storyQuality = 0f;
        public float overallQuality = 0f;
        
        [Header("Market Performance")]
        public long price = 60;
        public int totalUnitsSold = 0;
        public int monthlySales = 0;
        public bool isActive = true;
        
        [Header("Reception")]
        public float criticScore = 0f;
        public float userScore = 0f;
        public float marketReception = 0f;
        
        [Header("Features")]
        public List<string> features = new List<string>();
        public bool isMultiplayer = false;
        public bool hasOnlineFeatures = false;
        public int estimatedPlaytime = 10; // Hours
        
        public Game()
        {
            Initialize();
        }
        
        public Game(string gameTitle, GameGenre gameGenre, string console)
        {
            title = gameTitle;
            genre = gameGenre;
            targetConsole = console;
            Initialize();
        }
        
        private void Initialize()
        {
            features = new List<string>();
            developmentTeam = new List<string>();
            releaseYear = GameManager.Instance?.timeManager?.currentYear ?? 1980;
        }
        
        public void CalculateQuality(List<Employee> team)
        {
            if (team == null || team.Count == 0)
            {
                // Default quality if no team
                gameplayQuality = Random.Range(30f, 60f);
                graphicsQuality = Random.Range(30f, 60f);
                soundQuality = Random.Range(30f, 60f);
                storyQuality = Random.Range(30f, 60f);
            }
            else
            {
                // Calculate quality based on team skills
                float avgProgramming = 0f;
                float avgDesign = 0f;
                int teamSize = team.Count;
                
                foreach (Employee employee in team)
                {
                    avgProgramming += employee.programmingSkill * employee.GetProductivity();
                    avgDesign += employee.designSkill * employee.GetProductivity();
                }
                
                avgProgramming /= teamSize;
                avgDesign /= teamSize;
                
                // Quality based on skills and genre requirements
                gameplayQuality = CalculateGameplayQuality(avgProgramming, avgDesign);
                graphicsQuality = CalculateGraphicsQuality(avgDesign);
                soundQuality = CalculateSoundQuality(avgDesign);
                storyQuality = CalculateStoryQuality(avgDesign);
            }
            
            // Genre-specific adjustments
            ApplyGenreModifiers();
            
            // Calculate overall quality
            overallQuality = (gameplayQuality + graphicsQuality + soundQuality + storyQuality) / 4f;
            
            // Feature bonuses
            float featureBonus = features.Count * 2f;
            if (isMultiplayer) featureBonus += 5f;
            if (hasOnlineFeatures) featureBonus += 3f;
            
            overallQuality = Mathf.Clamp(overallQuality + featureBonus, 0f, 100f);
        }
        
        private float CalculateGameplayQuality(float programming, float design)
        {
            return (programming * 0.7f + design * 0.3f) + Random.Range(-10f, 10f);
        }
        
        private float CalculateGraphicsQuality(float design)
        {
            return design + Random.Range(-15f, 15f);
        }
        
        private float CalculateSoundQuality(float design)
        {
            return design * 0.8f + Random.Range(-10f, 10f);
        }
        
        private float CalculateStoryQuality(float design)
        {
            return design * 0.9f + Random.Range(-10f, 10f);
        }
        
        private void ApplyGenreModifiers()
        {
            switch (genre)
            {
                case GameGenre.Action:
                    gameplayQuality *= 1.2f;
                    graphicsQuality *= 1.1f;
                    storyQuality *= 0.8f;
                    break;
                case GameGenre.RPG:
                    storyQuality *= 1.3f;
                    gameplayQuality *= 1.1f;
                    estimatedPlaytime = Random.Range(40, 100);
                    break;
                case GameGenre.Strategy:
                    gameplayQuality *= 1.3f;
                    graphicsQuality *= 0.9f;
                    break;
                case GameGenre.Sports:
                    graphicsQuality *= 1.1f;
                    gameplayQuality *= 1.1f;
                    storyQuality *= 0.6f;
                    break;
                case GameGenre.Racing:
                    graphicsQuality *= 1.2f;
                    soundQuality *= 1.1f;
                    storyQuality *= 0.7f;
                    break;
                case GameGenre.Puzzle:
                    gameplayQuality *= 1.1f;
                    graphicsQuality *= 0.8f;
                    storyQuality *= 0.8f;
                    break;
                case GameGenre.Platformer:
                    gameplayQuality *= 1.2f;
                    graphicsQuality *= 1.0f;
                    break;
            }
            
            // Clamp all values
            gameplayQuality = Mathf.Clamp(gameplayQuality, 0f, 100f);
            graphicsQuality = Mathf.Clamp(graphicsQuality, 0f, 100f);
            soundQuality = Mathf.Clamp(soundQuality, 0f, 100f);
            storyQuality = Mathf.Clamp(storyQuality, 0f, 100f);
        }
        
        public void Launch()
        {
            isActive = true;
            
            // Calculate market reception
            marketReception = overallQuality;
            
            // Price factor
            float priceScore = 100f;
            if (price > 70) priceScore -= (price - 70) * 0.5f;
            else if (price < 40) priceScore -= (40 - price) * 0.3f;
            
            marketReception = (marketReception + priceScore) / 2f;
            
            // Marketing factor
            float marketingFactor = Mathf.Sqrt(marketingBudget / 5000f);
            marketReception += marketingFactor;
            
            marketReception = Mathf.Clamp(marketReception, 0f, 100f);
            
            // Generate review scores
            criticScore = overallQuality + Random.Range(-15f, 15f);
            criticScore = Mathf.Clamp(criticScore, 0f, 100f);
            
            userScore = marketReception + Random.Range(-10f, 10f);
            userScore = Mathf.Clamp(userScore, 0f, 100f);
        }
        
        public void SimulateMonthlySales()
        {
            if (!isActive) return;
            
            // Base sales from market reception
            float baseSales = marketReception * 500f;
            
            // Genre popularity factor
            float genrePopularity = GetGenrePopularity();
            baseSales *= genrePopularity;
            
            // Random factor
            float randomFactor = Random.Range(0.8f, 1.2f);
            
            // Age factor
            int age = (GameManager.Instance?.timeManager?.currentYear ?? releaseYear) - releaseYear;
            float ageFactor = Mathf.Max(0.05f, 1f - (age * 0.15f));
            
            monthlySales = Mathf.RoundToInt(baseSales * randomFactor * ageFactor);
            totalUnitsSold += monthlySales;
        }
        
        private float GetGenrePopularity()
        {
            // This could be dynamic based on market trends
            switch (genre)
            {
                case GameGenre.Action: return 1.2f;
                case GameGenre.RPG: return 1.1f;
                case GameGenre.Strategy: return 0.9f;
                case GameGenre.Sports: return 1.0f;
                case GameGenre.Racing: return 0.95f;
                case GameGenre.Puzzle: return 0.8f;
                case GameGenre.Platformer: return 1.0f;
                default: return 1.0f;
            }
        }
        
        public long GetTotalRevenue()
        {
            return totalUnitsSold * price;
        }
        
        public long GetTotalProfit()
        {
            return GetTotalRevenue() - developmentCost - marketingBudget;
        }
    }
    
    public enum GameGenre
    {
        Action,
        RPG,
        Strategy,
        Sports,
        Racing,
        Puzzle,
        Platformer,
        Adventure,
        Simulation,
        Fighting
    }
}
