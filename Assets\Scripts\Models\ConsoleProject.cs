using UnityEngine;
using System.Collections.Generic;

namespace ConsoleTycoon
{
    [System.Serializable]
    public class ConsoleProject
    {
        [Header("Project Information")]
        public string projectName;
        public string consoleName;
        public ProjectPhase currentPhase = ProjectPhase.Planning;
        public float progressPercentage = 0f;
        
        [Header("Timeline")]
        public int totalDevelopmentTime = 24; // Months
        public int monthsSpent = 0;
        public int estimatedMonthsRemaining = 24;
        
        [Header("Budget")]
        public long totalBudget = 1000000;
        public long spentBudget = 0;
        public long monthlyBurnRate = 50000;
        
        [Header("Team")]
        public List<string> assignedEmployees = new List<string>();
        public int requiredProgrammers = 3;
        public int requiredDesigners = 2;
        public int requiredManagers = 1;
        
        [Header("Technical Specifications")]
        public int targetCpuPower = 50;
        public int targetGraphicsPower = 50;
        public int targetSoundQuality = 50;
        public int targetMemorySize = 50;
        public int targetStorageCapacity = 50;
        
        [Header("Features")]
        public List<string> plannedFeatures = new List<string>();
        public List<string> completedFeatures = new List<string>();
        public bool includeOnlineCapability = false;
        public bool includeBackwardsCompatibility = false;
        public int controllerPorts = 2;
        
        [Header("Quality & Progress")]
        public float designQuality = 0f;
        public float engineeringQuality = 0f;
        public float manufacturingQuality = 0f;
        public float overallQuality = 0f;
        
        [Header("Risks & Issues")]
        public List<string> currentIssues = new List<string>();
        public float riskLevel = 0f; // 0-100, higher is more risky
        
        public ConsoleProject()
        {
            Initialize();
        }
        
        public ConsoleProject(string name, string console)
        {
            projectName = name;
            consoleName = console;
            Initialize();
        }
        
        private void Initialize()
        {
            assignedEmployees = new List<string>();
            plannedFeatures = new List<string>();
            completedFeatures = new List<string>();
            currentIssues = new List<string>();
            
            // Default planned features
            plannedFeatures.Add("Basic CPU");
            plannedFeatures.Add("Basic Graphics");
            plannedFeatures.Add("Basic Sound");
            plannedFeatures.Add("Controller Support");
        }
        
        public void UpdateProgress(List<Employee> team)
        {
            if (currentPhase == ProjectPhase.Completed || currentPhase == ProjectPhase.Cancelled)
                return;
            
            // Calculate monthly progress based on team
            float monthlyProgress = CalculateMonthlyProgress(team);
            
            // Apply progress
            progressPercentage += monthlyProgress;
            monthsSpent++;
            spentBudget += monthlyBurnRate;
            
            // Update estimated time remaining
            if (monthlyProgress > 0)
            {
                estimatedMonthsRemaining = Mathf.RoundToInt((100f - progressPercentage) / monthlyProgress);
            }
            
            // Check for phase transitions
            CheckPhaseTransition();
            
            // Update quality metrics
            UpdateQualityMetrics(team);
            
            // Check for issues
            CheckForIssues();
            
            // Check if project is complete
            if (progressPercentage >= 100f)
            {
                CompleteProject();
            }
        }
        
        private float CalculateMonthlyProgress(List<Employee> team)
        {
            if (team == null || team.Count == 0)
                return 0f;
            
            float baseProgress = 100f / totalDevelopmentTime; // Base monthly progress
            
            // Team efficiency
            float teamEfficiency = CalculateTeamEfficiency(team);
            
            // Phase modifier
            float phaseModifier = GetPhaseProgressModifier();
            
            // Budget constraint
            float budgetModifier = 1f;
            if (spentBudget >= totalBudget * 0.9f)
            {
                budgetModifier = 0.5f; // Slow down if running out of budget
            }
            
            return baseProgress * teamEfficiency * phaseModifier * budgetModifier;
        }
        
        private float CalculateTeamEfficiency(List<Employee> team)
        {
            float efficiency = 0f;
            int programmers = 0, designers = 0, managers = 0;
            
            foreach (Employee employee in team)
            {
                float employeeContribution = 0f;
                
                switch (employee.role)
                {
                    case EmployeeRole.Programmer:
                        programmers++;
                        employeeContribution = employee.programmingSkill * employee.GetProductivity();
                        break;
                    case EmployeeRole.Designer:
                        designers++;
                        employeeContribution = employee.designSkill * employee.GetProductivity();
                        break;
                    case EmployeeRole.ProjectManager:
                        managers++;
                        employeeContribution = employee.managementSkill * employee.GetProductivity();
                        break;
                }
                
                efficiency += employeeContribution;
            }
            
            // Team composition penalties
            float compositionPenalty = 1f;
            if (programmers < requiredProgrammers) compositionPenalty *= 0.8f;
            if (designers < requiredDesigners) compositionPenalty *= 0.9f;
            if (managers < requiredManagers) compositionPenalty *= 0.85f;
            
            // Normalize efficiency (assuming average skill of 50)
            efficiency = (efficiency / (team.Count * 50f)) * compositionPenalty;
            
            return Mathf.Clamp(efficiency, 0.1f, 2f);
        }
        
        private float GetPhaseProgressModifier()
        {
            switch (currentPhase)
            {
                case ProjectPhase.Planning: return 1.2f;
                case ProjectPhase.Design: return 1.0f;
                case ProjectPhase.Development: return 0.8f;
                case ProjectPhase.Testing: return 1.1f;
                case ProjectPhase.Manufacturing: return 0.9f;
                default: return 1.0f;
            }
        }
        
        private void CheckPhaseTransition()
        {
            switch (currentPhase)
            {
                case ProjectPhase.Planning:
                    if (progressPercentage >= 15f) currentPhase = ProjectPhase.Design;
                    break;
                case ProjectPhase.Design:
                    if (progressPercentage >= 35f) currentPhase = ProjectPhase.Development;
                    break;
                case ProjectPhase.Development:
                    if (progressPercentage >= 75f) currentPhase = ProjectPhase.Testing;
                    break;
                case ProjectPhase.Testing:
                    if (progressPercentage >= 90f) currentPhase = ProjectPhase.Manufacturing;
                    break;
                case ProjectPhase.Manufacturing:
                    if (progressPercentage >= 100f) currentPhase = ProjectPhase.Completed;
                    break;
            }
        }
        
        private void UpdateQualityMetrics(List<Employee> team)
        {
            if (team == null || team.Count == 0) return;
            
            float avgProgramming = 0f;
            float avgDesign = 0f;
            float avgManagement = 0f;
            
            foreach (Employee employee in team)
            {
                avgProgramming += employee.programmingSkill;
                avgDesign += employee.designSkill;
                avgManagement += employee.managementSkill;
            }
            
            avgProgramming /= team.Count;
            avgDesign /= team.Count;
            avgManagement /= team.Count;
            
            // Update quality based on current phase
            switch (currentPhase)
            {
                case ProjectPhase.Planning:
                case ProjectPhase.Design:
                    designQuality = avgDesign + Random.Range(-5f, 5f);
                    break;
                case ProjectPhase.Development:
                    engineeringQuality = avgProgramming + Random.Range(-5f, 5f);
                    break;
                case ProjectPhase.Manufacturing:
                    manufacturingQuality = avgManagement + Random.Range(-5f, 5f);
                    break;
            }
            
            overallQuality = (designQuality + engineeringQuality + manufacturingQuality) / 3f;
        }
        
        private void CheckForIssues()
        {
            // Clear old issues
            currentIssues.Clear();
            riskLevel = 0f;
            
            // Budget issues
            if (spentBudget >= totalBudget * 0.8f)
            {
                currentIssues.Add("Budget running low");
                riskLevel += 20f;
            }
            
            // Timeline issues
            if (monthsSpent > totalDevelopmentTime * 0.8f && progressPercentage < 80f)
            {
                currentIssues.Add("Behind schedule");
                riskLevel += 25f;
            }
            
            // Team issues
            if (assignedEmployees.Count < (requiredProgrammers + requiredDesigners + requiredManagers))
            {
                currentIssues.Add("Understaffed");
                riskLevel += 15f;
            }
            
            // Quality issues
            if (overallQuality < 40f)
            {
                currentIssues.Add("Quality concerns");
                riskLevel += 30f;
            }
            
            riskLevel = Mathf.Clamp(riskLevel, 0f, 100f);
        }
        
        private void CompleteProject()
        {
            currentPhase = ProjectPhase.Completed;
            progressPercentage = 100f;
            
            // Create the final console
            Console newConsole = CreateFinalConsole();
            
            // Add to company's released consoles
            if (GameManager.Instance?.playerCompany != null)
            {
                GameManager.Instance.playerCompany.releasedConsoles.Add(newConsole);
            }
        }
        
        private Console CreateFinalConsole()
        {
            Console console = new Console(consoleName);
            
            // Apply specifications (with quality modifiers)
            float qualityModifier = overallQuality / 100f;
            console.cpuPower = Mathf.RoundToInt(targetCpuPower * qualityModifier);
            console.graphicsPower = Mathf.RoundToInt(targetGraphicsPower * qualityModifier);
            console.soundQuality = Mathf.RoundToInt(targetSoundQuality * qualityModifier);
            console.memorySize = Mathf.RoundToInt(targetMemorySize * qualityModifier);
            console.storageCapacity = Mathf.RoundToInt(targetStorageCapacity * qualityModifier);
            
            // Apply features
            console.features = new List<string>(completedFeatures);
            console.hasOnlineCapability = includeOnlineCapability;
            console.hasBackwardsCompatibility = includeBackwardsCompatibility;
            console.controllerPorts = controllerPorts;
            
            // Set costs and development info
            console.developmentCost = spentBudget;
            console.developmentProject = this;
            
            return console;
        }
        
        public void CancelProject()
        {
            currentPhase = ProjectPhase.Cancelled;
        }
        
        public bool IsCompleted()
        {
            return currentPhase == ProjectPhase.Completed;
        }
        
        public bool IsCancelled()
        {
            return currentPhase == ProjectPhase.Cancelled;
        }
    }
    
    public enum ProjectPhase
    {
        Planning,
        Design,
        Development,
        Testing,
        Manufacturing,
        Completed,
        Cancelled
    }
}
