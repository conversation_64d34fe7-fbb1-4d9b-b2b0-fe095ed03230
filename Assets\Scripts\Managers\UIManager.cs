using UnityEngine;
using UnityEngine.UI;
using TMPro;
using System.Collections.Generic;

namespace ConsoleTycoon
{
    public class UIManager : MonoBehaviour
    {
        [Header("Main UI Panels")]
        public GameObject mainMenuPanel;
        public GameObject gameUIPanel;
        public GameObject consoleDevPanel;
        public GameObject gameDevPanel;
        public GameObject financePanel;
        public GameObject marketPanel;
        public GameObject employeePanel;
        
        [Header("HUD Elements")]
        public TextMeshProUGUI companyNameText;
        public TextMeshProUGUI currentMoneyText;
        public TextMeshProUGUI currentDateText;
        public TextMeshProUGUI reputationText;
        public TextMeshProUGUI marketShareText;
        
        [Header("Time Controls")]
        public Button pauseButton;
        public Button playButton;
        public Button fastForwardButton;
        public Slider timeSpeedSlider;
        
        [Header("Navigation")]
        public Button consoleDevButton;
        public Button gameDevButton;
        public Button financeButton;
        public Button marketButton;
        public Button employeeButton;
        
        [Header("Notification System")]
        public GameObject notificationPrefab;
        public Transform notificationParent;
        public List<GameObject> activeNotifications = new List<GameObject>();
        
        private GameManager gameManager;
        private CompanyData playerCompany;
        
        private void Start()
        {
            gameManager = GameManager.Instance;
            playerCompany = gameManager?.playerCompany;
            
            SetupUI();
            SetupEventListeners();
        }
        
        private void SetupUI()
        {
            // Initially show main menu
            ShowMainMenu();
            
            // Hide all game panels
            if (gameUIPanel) gameUIPanel.SetActive(false);
            if (consoleDevPanel) consoleDevPanel.SetActive(false);
            if (gameDevPanel) gameDevPanel.SetActive(false);
            if (financePanel) financePanel.SetActive(false);
            if (marketPanel) marketPanel.SetActive(false);
            if (employeePanel) employeePanel.SetActive(false);
        }
        
        private void SetupEventListeners()
        {
            // Time controls
            if (pauseButton) pauseButton.onClick.AddListener(PauseGame);
            if (playButton) playButton.onClick.AddListener(ResumeGame);
            if (fastForwardButton) fastForwardButton.onClick.AddListener(FastForward);
            if (timeSpeedSlider) timeSpeedSlider.onValueChanged.AddListener(OnTimeSpeedChanged);
            
            // Navigation buttons
            if (consoleDevButton) consoleDevButton.onClick.AddListener(() => ShowPanel("console"));
            if (gameDevButton) gameDevButton.onClick.AddListener(() => ShowPanel("game"));
            if (financeButton) financeButton.onClick.AddListener(() => ShowPanel("finance"));
            if (marketButton) marketButton.onClick.AddListener(() => ShowPanel("market"));
            if (employeeButton) employeeButton.onClick.AddListener(() => ShowPanel("employee"));
        }
        
        private void Update()
        {
            UpdateHUD();
        }
        
        private void UpdateHUD()
        {
            if (playerCompany == null || gameManager == null) return;
            
            // Update company info
            if (companyNameText) companyNameText.text = playerCompany.companyName;
            if (currentMoneyText) currentMoneyText.text = $"${playerCompany.currentMoney:N0}";
            if (reputationText) reputationText.text = $"Rep: {playerCompany.reputation:F1}";
            if (marketShareText) marketShareText.text = $"Market: {playerCompany.marketShare:F1}%";
            
            // Update date
            if (currentDateText && gameManager.timeManager)
            {
                currentDateText.text = gameManager.timeManager.CurrentDateString;
            }
            
            // Update time control buttons
            UpdateTimeControlButtons();
        }
        
        private void UpdateTimeControlButtons()
        {
            if (gameManager == null) return;
            
            bool isPaused = gameManager.isPaused;
            
            if (pauseButton) pauseButton.interactable = !isPaused;
            if (playButton) playButton.interactable = isPaused;
            if (fastForwardButton) fastForwardButton.interactable = !isPaused;
            
            if (timeSpeedSlider && !isPaused)
            {
                timeSpeedSlider.value = gameManager.gameSpeed;
            }
        }
        
        public void ShowMainMenu()
        {
            HideAllPanels();
            if (mainMenuPanel) mainMenuPanel.SetActive(true);
        }
        
        public void ShowMainGameUI()
        {
            HideAllPanels();
            if (gameUIPanel) gameUIPanel.SetActive(true);
        }
        
        public void ShowPanel(string panelName)
        {
            HideAllGamePanels();
            
            switch (panelName.ToLower())
            {
                case "console":
                    if (consoleDevPanel) consoleDevPanel.SetActive(true);
                    break;
                case "game":
                    if (gameDevPanel) gameDevPanel.SetActive(true);
                    break;
                case "finance":
                    if (financePanel) financePanel.SetActive(true);
                    break;
                case "market":
                    if (marketPanel) marketPanel.SetActive(true);
                    break;
                case "employee":
                    if (employeePanel) employeePanel.SetActive(true);
                    break;
            }
        }
        
        private void HideAllPanels()
        {
            if (mainMenuPanel) mainMenuPanel.SetActive(false);
            if (gameUIPanel) gameUIPanel.SetActive(false);
            HideAllGamePanels();
        }
        
        private void HideAllGamePanels()
        {
            if (consoleDevPanel) consoleDevPanel.SetActive(false);
            if (gameDevPanel) gameDevPanel.SetActive(false);
            if (financePanel) financePanel.SetActive(false);
            if (marketPanel) marketPanel.SetActive(false);
            if (employeePanel) employeePanel.SetActive(false);
        }
        
        // Time Control Methods
        public void PauseGame()
        {
            gameManager?.PauseGame();
        }
        
        public void ResumeGame()
        {
            gameManager?.ResumeGame();
        }
        
        public void FastForward()
        {
            gameManager?.SetGameSpeed(3f);
        }
        
        public void OnTimeSpeedChanged(float value)
        {
            gameManager?.SetGameSpeed(value);
        }
        
        // Main Menu Methods
        public void StartNewGame()
        {
            gameManager?.StartNewGame();
        }
        
        public void LoadGame()
        {
            // TODO: Implement load game functionality
            Debug.Log("Load game not yet implemented");
        }
        
        public void QuitGame()
        {
            Application.Quit();
        }
        
        // Notification System
        public void ShowNotification(string title, string message, NotificationType type = NotificationType.Info)
        {
            if (notificationPrefab == null || notificationParent == null) return;
            
            GameObject notification = Instantiate(notificationPrefab, notificationParent);
            
            // Set notification content (assuming the prefab has these components)
            var titleText = notification.transform.Find("Title")?.GetComponent<TextMeshProUGUI>();
            var messageText = notification.transform.Find("Message")?.GetComponent<TextMeshProUGUI>();
            var icon = notification.transform.Find("Icon")?.GetComponent<Image>();
            
            if (titleText) titleText.text = title;
            if (messageText) messageText.text = message;
            
            // Set icon color based on type
            if (icon)
            {
                switch (type)
                {
                    case NotificationType.Success:
                        icon.color = Color.green;
                        break;
                    case NotificationType.Warning:
                        icon.color = Color.yellow;
                        break;
                    case NotificationType.Error:
                        icon.color = Color.red;
                        break;
                    default:
                        icon.color = Color.blue;
                        break;
                }
            }
            
            activeNotifications.Add(notification);
            
            // Auto-remove after 5 seconds
            Destroy(notification, 5f);
        }
        
        public void ShowProjectCompleteNotification(string projectName, bool isConsole)
        {
            string title = isConsole ? "Console Complete!" : "Game Complete!";
            string message = $"{projectName} has finished development and is ready for launch!";
            ShowNotification(title, message, NotificationType.Success);
        }
        
        public void ShowFinancialAlert(string message)
        {
            ShowNotification("Financial Alert", message, NotificationType.Warning);
        }
        
        public void ShowMarketNews(string news)
        {
            ShowNotification("Market News", news, NotificationType.Info);
        }
        
        // Utility Methods
        public void UpdateCompanyName(string newName)
        {
            if (playerCompany != null)
            {
                playerCompany.companyName = newName;
            }
        }
        
        public void ShowConfirmDialog(string title, string message, System.Action onConfirm, System.Action onCancel = null)
        {
            // TODO: Implement confirmation dialog
            Debug.Log($"Confirm Dialog: {title} - {message}");
            onConfirm?.Invoke();
        }
        
        public void ShowInputDialog(string title, string placeholder, System.Action<string> onSubmit, System.Action onCancel = null)
        {
            // TODO: Implement input dialog
            Debug.Log($"Input Dialog: {title}");
            onSubmit?.Invoke("Default Input");
        }
        
        // Panel-specific update methods (to be called by individual panel scripts)
        public void RefreshConsolePanel()
        {
            // TODO: Refresh console development panel
        }
        
        public void RefreshGamePanel()
        {
            // TODO: Refresh game development panel
        }
        
        public void RefreshFinancePanel()
        {
            // TODO: Refresh finance panel
        }
        
        public void RefreshMarketPanel()
        {
            // TODO: Refresh market panel
        }
        
        public void RefreshEmployeePanel()
        {
            // TODO: Refresh employee panel
        }
    }
    
    public enum NotificationType
    {
        Info,
        Success,
        Warning,
        Error
    }
}
