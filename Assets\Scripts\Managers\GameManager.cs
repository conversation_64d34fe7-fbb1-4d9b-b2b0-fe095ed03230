using UnityEngine;
using System.Collections.Generic;

namespace ConsoleTycoon
{
    public class GameManager : MonoBehaviour
    {
        public static GameManager Instance { get; private set; }

        [Header("Game Settings")]
        public float gameSpeed = 1f;
        public bool isPaused = false;

        [Header("Managers")]
        public TimeManager timeManager;
        public FinanceManager financeManager;
        public UIManager uiManager;
        public ConsoleManager consoleManager;
        public GameDevManager gameDevManager;
        public MarketManager marketManager;

        [Header("Game State")]
        public GameState currentGameState = GameState.MainMenu;
        public CompanyData playerCompany;

        private void Awake()
        {
            // Singleton pattern
            if (Instance == null)
            {
                Instance = this;
                DontDestroyOnLoad(gameObject);
                InitializeGame();
            }
            else
            {
                Destroy(gameObject);
            }
        }

        private void InitializeGame()
        {
            // Initialize all managers
            if (timeManager == null) timeManager = GetComponent<TimeManager>();
            if (financeManager == null) financeManager = GetComponent<FinanceManager>();
            if (uiManager == null) uiManager = FindObjectOfType<UIManager>();
            if (consoleManager == null) consoleManager = GetComponent<ConsoleManager>();
            if (gameDevManager == null) gameDevManager = GetComponent<GameDevManager>();
            if (marketManager == null) marketManager = GetComponent<MarketManager>();

            // Initialize player company
            if (playerCompany == null)
            {
                playerCompany = ScriptableObject.CreateInstance<CompanyData>();
                playerCompany.Initialize();
            }
        }

        public void StartNewGame()
        {
            currentGameState = GameState.Playing;
            
            // Reset all systems
            timeManager?.StartGame();
            financeManager?.StartNewGame();
            consoleManager?.Initialize();
            gameDevManager?.Initialize();
            marketManager?.Initialize();
            
            // Initialize UI
            uiManager?.ShowMainGameUI();
            
            Debug.Log("New game started!");
        }

        public void PauseGame()
        {
            isPaused = true;
            Time.timeScale = 0f;
        }

        public void ResumeGame()
        {
            isPaused = false;
            Time.timeScale = gameSpeed;
        }

        public void SetGameSpeed(float speed)
        {
            gameSpeed = speed;
            if (!isPaused)
            {
                Time.timeScale = gameSpeed;
            }
        }

        public void SaveGame(string saveName)
        {
            // TODO: Implement save system
            Debug.Log($"Saving game: {saveName}");
        }

        public void LoadGame(string saveName)
        {
            // TODO: Implement load system
            Debug.Log($"Loading game: {saveName}");
        }

        public void QuitToMenu()
        {
            currentGameState = GameState.MainMenu;
            Time.timeScale = 1f;
            // TODO: Load main menu scene
        }

        private void Update()
        {
            if (currentGameState == GameState.Playing && !isPaused)
            {
                // Update all managers
                timeManager?.UpdateTime();
            }
        }
    }

    public enum GameState
    {
        MainMenu,
        Playing,
        Paused,
        GameOver
    }
}
