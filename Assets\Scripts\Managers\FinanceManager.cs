using UnityEngine;
using System.Collections.Generic;

namespace ConsoleTycoon
{
    public class FinanceManager : MonoBehaviour
    {
        [Header("Starting Finances")]
        public long startingMoney = 100000;
        public long startingLoanLimit = 500000;
        
        [Header("Current Financial State")]
        public long currentCash = 0;
        public long totalDebt = 0;
        public float interestRate = 0.05f; // 5% annual interest
        
        [Header("Financial History")]
        public List<FinancialRecord> monthlyRecords = new List<FinancialRecord>();
        public List<Transaction> recentTransactions = new List<Transaction>();
        public int maxTransactionHistory = 100;
        
        [Header("Loans and Credit")]
        public List<Loan> activeLoans = new List<Loan>();
        public long maxLoanAmount = 500000;
        public long availableCredit = 500000;
        
        [Header("Investments")]
        public List<Investment> activeInvestments = new List<Investment>();
        
        private CompanyData playerCompany;
        
        private void Start()
        {
            playerCompany = GameManager.Instance?.playerCompany;
        }
        
        public void StartNewGame()
        {
            currentCash = startingMoney;
            totalDebt = 0;
            availableCredit = maxLoanAmount;
            
            monthlyRecords.Clear();
            recentTransactions.Clear();
            activeLoans.Clear();
            activeInvestments.Clear();
            
            // Record starting transaction
            AddTransaction(TransactionType.Initial, startingMoney, "Starting capital");
            
            Debug.Log($"Finance Manager initialized with ${currentCash:N0}");
        }
        
        public bool CanAfford(long amount)
        {
            return currentCash >= amount;
        }
        
        public bool SpendMoney(long amount, string description, TransactionType type = TransactionType.Expense)
        {
            if (CanAfford(amount))
            {
                currentCash -= amount;
                AddTransaction(type, -amount, description);
                
                if (playerCompany != null)
                {
                    playerCompany.currentMoney = currentCash;
                    playerCompany.totalExpenses += amount;
                }
                
                return true;
            }
            
            Debug.LogWarning($"Cannot afford ${amount:N0}. Current cash: ${currentCash:N0}");
            return false;
        }
        
        public void EarnMoney(long amount, string description, TransactionType type = TransactionType.Revenue)
        {
            currentCash += amount;
            AddTransaction(type, amount, description);
            
            if (playerCompany != null)
            {
                playerCompany.currentMoney = currentCash;
                playerCompany.totalRevenue += amount;
            }
        }
        
        public bool TakeLoan(long amount, int termMonths = 24)
        {
            if (amount > availableCredit)
            {
                Debug.LogWarning($"Loan amount ${amount:N0} exceeds available credit ${availableCredit:N0}");
                return false;
            }
            
            Loan newLoan = new Loan
            {
                principal = amount,
                remainingBalance = amount,
                interestRate = interestRate,
                termMonths = termMonths,
                monthlyPayment = CalculateMonthlyPayment(amount, interestRate, termMonths),
                startDate = GameManager.Instance?.timeManager?.CurrentDateString ?? "Unknown"
            };
            
            activeLoans.Add(newLoan);
            totalDebt += amount;
            availableCredit -= amount;
            
            EarnMoney(amount, $"Loan: ${amount:N0} over {termMonths} months", TransactionType.Loan);
            
            return true;
        }
        
        public void ProcessMonthlyFinances()
        {
            long monthlyIncome = 0;
            long monthlyExpenses = 0;
            
            // Calculate income from sales
            if (playerCompany != null)
            {
                foreach (var console in playerCompany.releasedConsoles)
                {
                    if (console.isActive)
                    {
                        long consoleRevenue = console.monthlySales * console.price;
                        monthlyIncome += consoleRevenue;
                    }
                }
                
                foreach (var game in playerCompany.releasedGames)
                {
                    if (game.isActive)
                    {
                        long gameRevenue = game.monthlySales * game.price;
                        monthlyIncome += gameRevenue;
                    }
                }
                
                // Calculate expenses (salaries)
                foreach (var employee in playerCompany.employees)
                {
                    monthlyExpenses += employee.salary;
                }
            }
            
            // Process loan payments
            ProcessLoanPayments(ref monthlyExpenses);
            
            // Process investment returns
            ProcessInvestmentReturns(ref monthlyIncome);
            
            // Apply income and expenses
            if (monthlyIncome > 0)
            {
                EarnMoney(monthlyIncome, "Monthly sales revenue", TransactionType.Revenue);
            }
            
            if (monthlyExpenses > 0)
            {
                SpendMoney(monthlyExpenses, "Monthly operating expenses", TransactionType.Expense);
            }
            
            // Record monthly financial state
            RecordMonthlyFinances(monthlyIncome, monthlyExpenses);
        }
        
        private void ProcessLoanPayments(ref long monthlyExpenses)
        {
            for (int i = activeLoans.Count - 1; i >= 0; i--)
            {
                var loan = activeLoans[i];
                
                if (CanAfford(loan.monthlyPayment))
                {
                    // Calculate interest and principal portions
                    long interestPayment = (long)(loan.remainingBalance * (loan.interestRate / 12));
                    long principalPayment = loan.monthlyPayment - interestPayment;
                    
                    loan.remainingBalance -= principalPayment;
                    loan.monthsRemaining--;
                    totalDebt -= principalPayment;
                    
                    monthlyExpenses += loan.monthlyPayment;
                    
                    // Check if loan is paid off
                    if (loan.remainingBalance <= 0 || loan.monthsRemaining <= 0)
                    {
                        availableCredit += loan.principal;
                        activeLoans.RemoveAt(i);
                        AddTransaction(TransactionType.LoanPayment, -loan.monthlyPayment, "Final loan payment");
                    }
                }
                else
                {
                    // Cannot make payment - add penalty
                    loan.remainingBalance += (long)(loan.remainingBalance * 0.02f); // 2% penalty
                    AddTransaction(TransactionType.Penalty, -1000, "Missed loan payment penalty");
                }
            }
        }
        
        private void ProcessInvestmentReturns(ref long monthlyIncome)
        {
            foreach (var investment in activeInvestments)
            {
                long returns = (long)(investment.amount * investment.monthlyReturnRate);
                monthlyIncome += returns;
                investment.totalReturns += returns;
            }
        }
        
        private void RecordMonthlyFinances(long income, long expenses)
        {
            FinancialRecord record = new FinancialRecord
            {
                date = GameManager.Instance?.timeManager?.CurrentDateString ?? "Unknown",
                income = income,
                expenses = expenses,
                netProfit = income - expenses,
                cashBalance = currentCash,
                totalDebt = totalDebt
            };
            
            monthlyRecords.Add(record);
            
            // Keep only last 24 months of records
            if (monthlyRecords.Count > 24)
            {
                monthlyRecords.RemoveAt(0);
            }
        }
        
        private void AddTransaction(TransactionType type, long amount, string description)
        {
            Transaction transaction = new Transaction
            {
                type = type,
                amount = amount,
                description = description,
                date = GameManager.Instance?.timeManager?.CurrentDateString ?? "Unknown",
                balanceAfter = currentCash
            };
            
            recentTransactions.Add(transaction);
            
            // Keep only recent transactions
            if (recentTransactions.Count > maxTransactionHistory)
            {
                recentTransactions.RemoveAt(0);
            }
        }
        
        private long CalculateMonthlyPayment(long principal, float annualRate, int months)
        {
            float monthlyRate = annualRate / 12f;
            if (monthlyRate == 0) return principal / months;
            
            float payment = principal * (monthlyRate * Mathf.Pow(1 + monthlyRate, months)) / 
                           (Mathf.Pow(1 + monthlyRate, months) - 1);
            
            return (long)payment;
        }
        
        public bool MakeInvestment(long amount, float expectedAnnualReturn, string description)
        {
            if (!CanAfford(amount)) return false;
            
            Investment investment = new Investment
            {
                amount = amount,
                description = description,
                monthlyReturnRate = expectedAnnualReturn / 12f,
                startDate = GameManager.Instance?.timeManager?.CurrentDateString ?? "Unknown"
            };
            
            activeInvestments.Add(investment);
            SpendMoney(amount, $"Investment: {description}", TransactionType.Investment);
            
            return true;
        }
        
        public long GetNetWorth()
        {
            long assets = currentCash;
            
            // Add investment values
            foreach (var investment in activeInvestments)
            {
                assets += investment.amount + investment.totalReturns;
            }
            
            // Add company asset values (simplified)
            if (playerCompany != null)
            {
                foreach (var console in playerCompany.releasedConsoles)
                {
                    assets += console.developmentCost / 10; // Depreciated value
                }
            }
            
            return assets - totalDebt;
        }
        
        public float GetDebtToIncomeRatio()
        {
            if (monthlyRecords.Count == 0) return 0f;
            
            var lastRecord = monthlyRecords[monthlyRecords.Count - 1];
            if (lastRecord.income <= 0) return float.MaxValue;
            
            long monthlyDebtPayment = 0;
            foreach (var loan in activeLoans)
            {
                monthlyDebtPayment += loan.monthlyPayment;
            }
            
            return (float)monthlyDebtPayment / lastRecord.income;
        }
        
        public List<FinancialRecord> GetFinancialHistory(int months = 12)
        {
            int startIndex = Mathf.Max(0, monthlyRecords.Count - months);
            return monthlyRecords.GetRange(startIndex, monthlyRecords.Count - startIndex);
        }
    }
    
    [System.Serializable]
    public class FinancialRecord
    {
        public string date;
        public long income;
        public long expenses;
        public long netProfit;
        public long cashBalance;
        public long totalDebt;
    }
    
    [System.Serializable]
    public class Transaction
    {
        public TransactionType type;
        public long amount;
        public string description;
        public string date;
        public long balanceAfter;
    }
    
    [System.Serializable]
    public class Loan
    {
        public long principal;
        public long remainingBalance;
        public float interestRate;
        public int termMonths;
        public int monthsRemaining;
        public long monthlyPayment;
        public string startDate;
    }
    
    [System.Serializable]
    public class Investment
    {
        public long amount;
        public string description;
        public float monthlyReturnRate;
        public long totalReturns;
        public string startDate;
    }
    
    public enum TransactionType
    {
        Initial,
        Revenue,
        Expense,
        Loan,
        LoanPayment,
        Investment,
        Penalty,
        Bonus
    }
}
