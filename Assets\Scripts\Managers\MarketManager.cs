using UnityEngine;
using System.Collections.Generic;

namespace ConsoleTycoon
{
    public class MarketManager : MonoBehaviour
    {
        [Header("Market State")]
        public long totalMarketSize = 10000000; // Total potential customers
        public float marketGrowthRate = 0.1f; // 10% annual growth
        public List<CompetitorCompany> competitors = new List<CompetitorCompany>();
        
        [Header("Market Consoles")]
        public List<Console> marketConsoles = new List<Console>(); // All consoles in market
        public List<Game> marketGames = new List<Game>(); // All games in market
        
        [Header("Market Trends")]
        public List<MarketEvent> activeEvents = new List<MarketEvent>();
        public float economicCondition = 1.0f; // 0.5 = recession, 1.0 = normal, 1.5 = boom
        
        private CompanyData playerCompany;
        
        private void Start()
        {
            playerCompany = GameManager.Instance?.playerCompany;
        }
        
        public void Initialize()
        {
            InitializeCompetitors();
            InitializeMarketEvents();
            CalculateMarketSize();
            
            Debug.Log("Market Manager initialized");
        }
        
        private void InitializeCompetitors()
        {
            competitors.Clear();
            
            // Add historical competitors based on current year
            int currentYear = GameManager.Instance?.timeManager?.currentYear ?? 1980;
            
            if (currentYear >= 1980)
            {
                competitors.Add(new CompetitorCompany
                {
                    name = "Atari",
                    marketShare = 0.4f,
                    reputation = 75f,
                    aggressiveness = 0.7f,
                    focusArea = CompetitorFocus.Consoles,
                    budget = 5000000
                });
            }
            
            if (currentYear >= 1983)
            {
                competitors.Add(new CompetitorCompany
                {
                    name = "Nintendo",
                    marketShare = 0.3f,
                    reputation = 80f,
                    aggressiveness = 0.8f,
                    focusArea = CompetitorFocus.Both,
                    budget = 8000000
                });
            }
            
            if (currentYear >= 1985)
            {
                competitors.Add(new CompetitorCompany
                {
                    name = "Sega",
                    marketShare = 0.2f,
                    reputation = 70f,
                    aggressiveness = 0.9f,
                    focusArea = CompetitorFocus.Consoles,
                    budget = 6000000
                });
            }
            
            if (currentYear >= 1995)
            {
                competitors.Add(new CompetitorCompany
                {
                    name = "Sony",
                    marketShare = 0.25f,
                    reputation = 85f,
                    aggressiveness = 0.8f,
                    focusArea = CompetitorFocus.Consoles,
                    budget = 12000000
                });
            }
            
            if (currentYear >= 2001)
            {
                competitors.Add(new CompetitorCompany
                {
                    name = "Microsoft",
                    marketShare = 0.2f,
                    reputation = 75f,
                    aggressiveness = 0.9f,
                    focusArea = CompetitorFocus.Both,
                    budget = 15000000
                });
            }
        }
        
        private void InitializeMarketEvents()
        {
            activeEvents.Clear();
            
            // Add some random market events
            if (Random.Range(0f, 1f) < 0.3f)
            {
                activeEvents.Add(new MarketEvent
                {
                    name = "Holiday Season Boost",
                    description = "Increased sales during holiday season",
                    salesMultiplier = 1.5f,
                    duration = 2,
                    affectsConsoles = true,
                    affectsGames = true
                });
            }
            
            if (Random.Range(0f, 1f) < 0.1f)
            {
                activeEvents.Add(new MarketEvent
                {
                    name = "Economic Recession",
                    description = "Economic downturn affecting luxury purchases",
                    salesMultiplier = 0.7f,
                    duration = 6,
                    affectsConsoles = true,
                    affectsGames = false
                });
            }
        }
        
        public void ProcessAnnualMarketChanges()
        {
            // Update market size
            totalMarketSize = (long)(totalMarketSize * (1 + marketGrowthRate));
            
            // Update competitor market shares
            UpdateCompetitorShares();
            
            // Add new competitors if needed
            AddNewCompetitors();
            
            // Update economic conditions
            UpdateEconomicConditions();
            
            Debug.Log($"Market size updated to {totalMarketSize:N0} potential customers");
        }
        
        private void UpdateCompetitorShares()
        {
            foreach (var competitor in competitors)
            {
                // Simulate competitor performance
                float performance = Random.Range(0.8f, 1.2f);
                competitor.marketShare *= performance;
                
                // Adjust reputation based on performance
                if (performance > 1.1f)
                {
                    competitor.reputation += Random.Range(1f, 5f);
                }
                else if (performance < 0.9f)
                {
                    competitor.reputation -= Random.Range(1f, 3f);
                }
                
                competitor.reputation = Mathf.Clamp(competitor.reputation, 20f, 100f);
                competitor.marketShare = Mathf.Clamp(competitor.marketShare, 0.01f, 0.6f);
            }
            
            // Normalize market shares
            NormalizeMarketShares();
        }
        
        private void NormalizeMarketShares()
        {
            float totalShare = 0f;
            
            // Include player company
            if (playerCompany != null)
            {
                totalShare += playerCompany.marketShare / 100f;
            }
            
            foreach (var competitor in competitors)
            {
                totalShare += competitor.marketShare;
            }
            
            if (totalShare > 1f)
            {
                // Normalize all shares
                float normalizationFactor = 1f / totalShare;
                
                if (playerCompany != null)
                {
                    playerCompany.marketShare *= normalizationFactor * 100f;
                }
                
                foreach (var competitor in competitors)
                {
                    competitor.marketShare *= normalizationFactor;
                }
            }
        }
        
        private void AddNewCompetitors()
        {
            int currentYear = GameManager.Instance?.timeManager?.currentYear ?? 1980;
            
            // Add new competitors based on year and market conditions
            if (currentYear >= 1990 && competitors.Count < 6 && Random.Range(0f, 1f) < 0.2f)
            {
                string[] newCompanyNames = { "TechCorp", "GameWorks", "PixelSoft", "RetroGaming", "FuturePlay" };
                string newName = newCompanyNames[Random.Range(0, newCompanyNames.Length)];
                
                // Check if name already exists
                if (competitors.Find(c => c.name == newName) == null)
                {
                    competitors.Add(new CompetitorCompany
                    {
                        name = newName,
                        marketShare = 0.05f,
                        reputation = Random.Range(40f, 70f),
                        aggressiveness = Random.Range(0.5f, 1.0f),
                        focusArea = (CompetitorFocus)Random.Range(0, 3),
                        budget = Random.Range(1000000, 5000000)
                    });
                    
                    Debug.Log($"New competitor entered market: {newName}");
                }
            }
        }
        
        private void UpdateEconomicConditions()
        {
            // Simulate economic fluctuations
            float change = Random.Range(-0.1f, 0.1f);
            economicCondition = Mathf.Clamp(economicCondition + change, 0.5f, 1.5f);
            
            if (economicCondition < 0.8f)
            {
                Debug.Log("Economic conditions are poor - sales may be affected");
            }
            else if (economicCondition > 1.2f)
            {
                Debug.Log("Economic boom - increased consumer spending!");
            }
        }
        
        public void UpdateCompetitors()
        {
            foreach (var competitor in competitors)
            {
                // Simulate competitor actions
                SimulateCompetitorActions(competitor);
            }
        }
        
        private void SimulateCompetitorActions(CompetitorCompany competitor)
        {
            // Chance to release new console
            if (Random.Range(0f, 1f) < 0.1f && competitor.focusArea != CompetitorFocus.Games)
            {
                ReleaseCompetitorConsole(competitor);
            }
            
            // Chance to release new game
            if (Random.Range(0f, 1f) < 0.3f && competitor.focusArea != CompetitorFocus.Consoles)
            {
                ReleaseCompetitorGame(competitor);
            }
            
            // Update budget based on performance
            long revenue = (long)(totalMarketSize * competitor.marketShare * 50); // Average $50 per customer
            competitor.budget += revenue / 2; // Keep half as profit, spend half
        }
        
        private void ReleaseCompetitorConsole(CompetitorCompany competitor)
        {
            Console newConsole = new Console($"{competitor.name} Console {Random.Range(1000, 9999)}");
            
            // Set specs based on competitor reputation and budget
            float qualityFactor = competitor.reputation / 100f;
            newConsole.cpuPower = Mathf.RoundToInt(Random.Range(40, 80) * qualityFactor);
            newConsole.graphicsPower = Mathf.RoundToInt(Random.Range(40, 80) * qualityFactor);
            newConsole.soundQuality = Mathf.RoundToInt(Random.Range(40, 80) * qualityFactor);
            newConsole.price = Random.Range(199, 499);
            
            newConsole.Launch();
            marketConsoles.Add(newConsole);
            
            Debug.Log($"{competitor.name} released {newConsole.name}");
        }
        
        private void ReleaseCompetitorGame(CompetitorCompany competitor)
        {
            GameGenre randomGenre = (GameGenre)Random.Range(0, System.Enum.GetValues(typeof(GameGenre)).Length);
            Game newGame = new Game($"{competitor.name} {randomGenre} Game", randomGenre, "Multi-platform");
            
            // Set quality based on competitor reputation
            float qualityFactor = competitor.reputation / 100f;
            newGame.overallQuality = Random.Range(40f, 90f) * qualityFactor;
            newGame.price = Random.Range(40, 70);
            
            newGame.Launch();
            marketGames.Add(newGame);
            
            Debug.Log($"{competitor.name} released {newGame.title}");
        }
        
        public void AddPlayerConsoleToMarket(Console console)
        {
            if (console != null && !marketConsoles.Contains(console))
            {
                marketConsoles.Add(console);
                UpdatePlayerMarketShare();
            }
        }
        
        public void AddPlayerGameToMarket(Game game)
        {
            if (game != null && !marketGames.Contains(game))
            {
                marketGames.Add(game);
                UpdatePlayerMarketShare();
            }
        }
        
        private void UpdatePlayerMarketShare()
        {
            if (playerCompany == null) return;
            
            long playerSales = 0;
            long totalMarketSales = 0;
            
            // Calculate player sales
            foreach (var console in playerCompany.releasedConsoles)
            {
                playerSales += console.totalUnitsSold;
            }
            
            foreach (var game in playerCompany.releasedGames)
            {
                playerSales += game.totalUnitsSold;
            }
            
            // Estimate total market sales (simplified)
            totalMarketSales = playerSales;
            foreach (var competitor in competitors)
            {
                totalMarketSales += (long)(totalMarketSize * competitor.marketShare * 0.1f); // Rough estimate
            }
            
            if (totalMarketSales > 0)
            {
                playerCompany.marketShare = ((float)playerSales / totalMarketSales) * 100f;
            }
        }
        
        public List<string> GetAvailableConsoles()
        {
            var consoles = new List<string>();
            
            foreach (var console in marketConsoles)
            {
                if (console.isActive)
                {
                    consoles.Add(console.name);
                }
            }
            
            return consoles;
        }
        
        public void ProcessMarketEvents()
        {
            for (int i = activeEvents.Count - 1; i >= 0; i--)
            {
                var marketEvent = activeEvents[i];
                marketEvent.duration--;
                
                if (marketEvent.duration <= 0)
                {
                    activeEvents.RemoveAt(i);
                    Debug.Log($"Market event ended: {marketEvent.name}");
                }
            }
        }
        
        public float GetMarketSalesMultiplier(bool isConsole)
        {
            float multiplier = economicCondition;
            
            foreach (var marketEvent in activeEvents)
            {
                if ((isConsole && marketEvent.affectsConsoles) || (!isConsole && marketEvent.affectsGames))
                {
                    multiplier *= marketEvent.salesMultiplier;
                }
            }
            
            return multiplier;
        }
        
        private void CalculateMarketSize()
        {
            int currentYear = GameManager.Instance?.timeManager?.currentYear ?? 1980;
            
            // Market size grows over time
            long baseMarketSize = 1000000; // 1 million in 1980
            float yearMultiplier = 1f + ((currentYear - 1980) * 0.15f); // 15% growth per year
            
            totalMarketSize = (long)(baseMarketSize * yearMultiplier);
        }
    }
    
    [System.Serializable]
    public class CompetitorCompany
    {
        public string name;
        public float marketShare; // 0-1 scale
        public float reputation; // 0-100 scale
        public float aggressiveness; // 0-1 scale, affects how often they release products
        public CompetitorFocus focusArea;
        public long budget;
    }
    
    [System.Serializable]
    public class MarketEvent
    {
        public string name;
        public string description;
        public float salesMultiplier;
        public int duration; // Months
        public bool affectsConsoles;
        public bool affectsGames;
    }
    
    public enum CompetitorFocus
    {
        Consoles,
        Games,
        Both
    }
}
